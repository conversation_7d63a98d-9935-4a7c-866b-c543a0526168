import React from 'react';
import { Link } from 'react-router-dom';
import { getCloudImageUrl } from '@/utils/gcs';

interface Demo {
  id: string;
  title: string;
  description: string;
  link: string;
  image?: string;
}

interface DemoCardProps {
  demo: Demo;
}

const DemoCard: React.FC<DemoCardProps> = ({ demo }) => {
  return (
    <Link to={demo.link} className='group block'>
      <div className='relative aspect-w-16 aspect-h-9 max-h-80 rounded-2xl overflow-hidden'>
        {demo.image ? (
          <img
            className='w-full h-full object-cover transition-all duration-300 group-hover:scale-105'
            src={getCloudImageUrl(demo.image)}
            alt={demo.title}
          />
        ) : (
          <div
            className={`w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-400 to-gray-600`}
          >
            <div className='relative flex items-center justify-center w-full h-full'>
              <p className='text-white text-2xl'>Demo</p>
            </div>
          </div>
        )}
        <div className='absolute inset-0 bg-blue-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300'></div>
        <div className='absolute inset-0 rounded-2xl shadow-inner-glow shadow-inner-glow-white pointer-events-none'></div>
      </div>
      <div className='mt-6'>
        <h2
          className={`text-2xl font-bold mt-2 text-gray-800 group-hover:text-blue-500 transition-colors duration-300 group-hover:drop-shadow-glow-blue-500`}
        >
          {demo.title}
        </h2>
        <p className='mt-4 text-lg text-gray-600 transition-all duration-300 group-hover:drop-shadow-glow-blue-500'>
          {demo.description}
        </p>
      </div>
    </Link>
  );
};

export default DemoCard;
