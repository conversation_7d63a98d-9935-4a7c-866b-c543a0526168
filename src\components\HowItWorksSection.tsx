import React, { useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faComments,
  faCog,
  faHandshake,
} from '@fortawesome/free-solid-svg-icons';
import { gsap } from 'gsap';
import { useGSAP } from '@gsap/react';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Registramos el plugin de ScrollTrigger
gsap.registerPlugin(ScrollTrigger);

export default function HowItWorksSection() {
  const main = useRef();

  // Hook de useGSAP para las animaciones
  useGSAP(
    () => {
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: main.current,
          start: 'top 99%', // La animación empieza cuando el top del elemento está al 80% del viewport
          toggleActions: 'play none none none',
          once: true, // Asegura que la animación solo se reproduzca una vez
        },
      });

      tl.from('.gsap-title', {
        duration: 0.4,
        opacity: 0,
        ease: 'power3.out',
      })
        .from(
          '.gsap-description',
          {
            duration: 0.4,
            opacity: 0,
            ease: 'power3.out',
          },
          '-=0.5' // Empieza 0.5s antes de que termine la animación anterior
        )
        .to('.gsap-card', {
          duration: 0.4,
          opacity: 1,
          y: 0, // Animate to original position
          ease: 'power3.out',
          stagger: 0.2,
        });
    },
    { scope: main }
  ); // El scope limita las querys de GSAP a los elementos dentro del ref 'main'

  const steps = [
    {
      icon: faComments,
      title: 'Consulta Gratuita',
      description:
        'Analizamos tus necesidades y te ofrecemos una solución personalizada sin compromiso.',
      timeframe: '5 min a 30 mins',
    },
    {
      icon: faCog,
      title: 'Diseño e Implementación',
      description:
        'Desarrollamos e integramos la solución de IA adaptada a tu negocio y tus necesidades.',
      timeframe: '0 a 7 días',
    },
    {
      icon: faHandshake,
      title: 'Soporte y Optimización',
      description:
        'Te acompañamos en todo momento, asegurando el rendimiento óptimo de tu nueva solución.',
      timeframe: 'Ilimitado',
    },
  ];

  return (
    <section
      ref={main}
      className='relative py-24 bg-[radial-gradient(circle_at_center,_white_0%,_white_50%,_rgba(236,72,153,0.2)_75%,_rgba(168,85,247,0.2)_100%)] shadow-inner-glow shadow-inner-glow-white-2xl overflow-hidden'
    >
      <div className='absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(255,255,255,0)_50%,rgba(255,255,255,1)_70%)]'></div>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative'>
        <div className='text-center mb-20'>
          <h2 className='gsap-title text-5xl md:text-6xl font-bold tracking-tight text-gray-900 text-center p-2 mb-6 drop-shadow-glow-white-2xl text-shadow-glow-white-2xl'>
            ¿Cómo trabajamos?
          </h2>
          <p className='gsap-description text-xl text-gray-800 max-w-3xl mx-auto leading-relaxed'>
            Nuestro proceso está diseñado para garantizar resultados
            excepcionales en cada proyecto
          </p>
        </div>

        {/* Desktop and Mobile Layout */}
        <div className='grid grid-cols-1 lg:grid-cols-3 gap-8 relative items-stretch'>
          {/* Desktop connecting line */}
          <div className='hidden lg:block absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-purple-400 to-pink-400 transform -translate-y-1/2 z-0'></div>

          {steps.map((step, index) => (
            <div
              key={index}
              className='gsap-card group relative p-6 rounded-3xl bg-white text-center transform transition-all duration-500 hover:scale-105 drop-shadow-glow-purple-500-bottom shadow-inner-glow shadow-inner-glow-purple-100-md opacity-0 translate-y-12'
            >
              {/* Vertical connecting line for mobile */}
              {index < steps.length - 1 && (
                <div className='lg:hidden absolute top-full left-1/2 w-0.5 h-8 bg-gradient-to-b from-purple-400 to-pink-400 transform -translate-x-1/2'></div>
              )}
              {/* Desktop connecting dot */}
              <div className='hidden lg:block absolute top-1/2 left-0 w-4 h-4 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 transform -translate-x-1/2 -translate-y-1/2 z-10'></div>
              {/* Gradient border effect */}
              <div className='absolute inset-0 rounded-3xl bg-gradient-to-r from-purple-400 to-pink-400 p-[2px] opacity-0 group-hover:opacity-100 transition-opacity duration-300 drop-shadow-glow-pink-600'>
                <div className='h-full w-full rounded-3xl bg-white'></div>
              </div>

              <div className='relative z-10 drop-shadow-glow-white flex flex-col items-center justify-between h-full'>
                {/* Icon container */}
                <div className='relative w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300'>
                  <FontAwesomeIcon
                    icon={step.icon}
                    className='w-10 h-10 text-white'
                  />
                </div>

                {/* Step number inside card, top-left */}
                <div className='absolute -top-2 -left-2 w-12 h-12 bg-gray-900 text-white rounded-full flex items-center justify-center text-base font-bold shadow-lg border-2 border-white z-20'>
                  {index + 1}
                </div>

                <h3 className='text-2xl font-bold text-gray-900 mb-4 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-purple-600 group-hover:to-pink-600 transition-all duration-300'>
                  {step.title}
                </h3>
                <p className='text-gray-800 leading-relaxed text-base flex-grow'>
                  {step.description}
                </p>
                <p className='text-sm font-semibold text-gray-600 mt-2'>
                  Plazo: {step.timeframe}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
