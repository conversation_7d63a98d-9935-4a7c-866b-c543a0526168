import React from 'react';

interface BackgroundWrapperProps {
  children: React.ReactNode;
  backgroundClasses?: string; // Nueva prop para clases de fondo
}

const BackgroundWrapper: React.FC<BackgroundWrapperProps> = ({
  children,
  backgroundClasses,
}) => {
  const defaultClasses =
    'iphone-xr-pro-max:py-2 shadow-inner-glow shadow-inner-glow-white-left-2xl shadow-inner-glow-white-right-2xl';
  const finalClasses = backgroundClasses
    ? `${defaultClasses} ${backgroundClasses}`
    : defaultClasses;

  return <div className={finalClasses}>{children}</div>;
};

export default BackgroundWrapper;
