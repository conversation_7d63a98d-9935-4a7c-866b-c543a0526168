import React from 'react';

const HeroSectionAgentesDeChat = () => {
  return (
    <header className='relative mt-2 pb-6 bg-gradient-to-b from-white via-pink-500 via-70% to-purple-800 shadow-inner-glow shadow-inner-glow shadow-inner-glow-white-right-2xl shadow-inner-glow-white-left-2xl'>
      <div className='max-w-7xl mx-auto px-6 sm:px-6 lg:px-8 text-center mt-6'>
        <h1
          className='text-6xl font-bold tracking-xl font-extrabold tracking-tight
          text-white drop-shadow-glow-purple-700-md text-shadow-glow-pink-900-sm'
        >
          Agentes de Chat IA Multicanal
        </h1>
        <p className='mt-2 text-lg md:text-xl mx-auto text-white drop-shadow-glow-purple-700-md max-w-6xl text-shadow-glow-pink-900-sm'>
          Automatiza y optimiza la comunicación con tus clientes 24/7 con
          soluciones de chatbot inteligentes y personalizadas.
        </p>
      </div>
    </header>
  );
};

export default HeroSectionAgentesDeChat;
