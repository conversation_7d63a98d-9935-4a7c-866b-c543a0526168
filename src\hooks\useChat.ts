import { useState, useEffect } from 'react';

interface Message {
  id: number;
  text?: string;
  sender: 'bot' | 'user';
  audioBlob?: Blob;
  file?: File;
}

const STORAGE_KEYS = {
  MESSAGES: 'chat_messages',
  CHAT_OPENED: 'chat_opened_before',
};

// Función para serializar mensajes (sin audioBlob y file para localStorage)
const serializeMessages = (messages: Message[]): any[] => {
  return messages.map(msg => ({
    id: msg.id,
    text: msg.text,
    sender: msg.sender,
    // No guardamos audioBlob ni file en localStorage
  }));
};

// Función para deserializar mensajes
const deserializeMessages = (serializedMessages: any[]): Message[] => {
  return serializedMessages.map(msg => ({
    id: msg.id,
    text: msg.text,
    sender: msg.sender,
  }));
};

export const useChat = () => {
  // Inicializar mensajes desde localStorage o con mensaje de bienvenida
  const [messages, setMessages] = useState<Message[]>(() => {
    try {
      const savedMessages = localStorage.getItem(STORAGE_KEYS.MESSAGES);
      const chatOpenedBefore = localStorage.getItem(STORAGE_KEYS.CHAT_OPENED);

      if (savedMessages && chatOpenedBefore) {
        // Si ya se abrió el chat antes y hay mensajes guardados, cargarlos
        return deserializeMessages(JSON.parse(savedMessages));
      } else if (chatOpenedBefore) {
        // Si ya se abrió el chat antes pero no hay mensajes, empezar vacío
        return [];
      } else {
        // Primera vez que se abre el chat, mostrar mensaje de bienvenida
        return [
          { id: 1, text: '¡Hola! ¿Cómo puedo ayudarte hoy?', sender: 'bot' },
        ];
      }
    } catch (error) {
      console.error('Error loading chat history:', error);
      return [
        { id: 1, text: '¡Hola! ¿Cómo puedo ayudarte hoy?', sender: 'bot' },
      ];
    }
  });

  const [isTyping, setIsTyping] = useState(false);

  // Marcar que el chat se ha abierto
  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.CHAT_OPENED, 'true');
  }, []);

  // Guardar mensajes en localStorage cuando cambien
  useEffect(() => {
    try {
      const serializedMessages = serializeMessages(messages);
      localStorage.setItem(
        STORAGE_KEYS.MESSAGES,
        JSON.stringify(serializedMessages)
      );
    } catch (error) {
      console.error('Error saving chat history:', error);
    }
  }, [messages]);

  const sendMessage = (text?: string, audioBlob?: Blob, file?: File) => {
    if (!text && !audioBlob && !file) return;

    const newMessage: Message = {
      id: Date.now(),
      text,
      sender: 'user',
      audioBlob,
      file,
    };
    setMessages(prev => [...prev, newMessage]);

    setIsTyping(true);
    setTimeout(() => {
      const botResponse: Message = {
        id: Date.now() + 1,
        text: 'Gracias por tu mensaje. Estoy procesando tu solicitud.',
        sender: 'bot',
      };
      setMessages(prev => [...prev, botResponse]);
      setIsTyping(false);
    }, 2000);
  };

  // Función para limpiar el historial del chat
  const clearChatHistory = () => {
    try {
      localStorage.removeItem(STORAGE_KEYS.MESSAGES);
      localStorage.removeItem(STORAGE_KEYS.CHAT_OPENED);
      setMessages([
        { id: 1, text: '¡Hola! ¿Cómo puedo ayudarte hoy?', sender: 'bot' },
      ]);
    } catch (error) {
      console.error('Error clearing chat history:', error);
    }
  };

  return { messages, isTyping, sendMessage, clearChatHistory };
};
