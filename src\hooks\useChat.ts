import { useState } from 'react';

interface Message {
  id: number;
  text?: string;
  sender: 'bot' | 'user';
  audioBlob?: Blob;
  file?: File;
}

export const useChat = () => {
  const [messages, setMessages] = useState<Message[]>([
    { id: 1, text: '¡Hola! ¿Cómo puedo ayudarte hoy?', sender: 'bot' },
  ]);
  const [isTyping, setIsTyping] = useState(false);

  const sendMessage = (text?: string, audioBlob?: Blob, file?: File) => {
    if (!text && !audioBlob && !file) return;

    const newMessage: Message = {
      id: Date.now(),
      text,
      sender: 'user',
      audioBlob,
      file,
    };
    setMessages(prev => [...prev, newMessage]);

    setIsTyping(true);
    setTimeout(() => {
      const botResponse: Message = {
        id: Date.now() + 1,
        text: '<PERSON><PERSON><PERSON> por tu mensaje. Estoy procesando tu solicitud.',
        sender: 'bot',
      };
      setMessages(prev => [...prev, botResponse]);
      setIsTyping(false);
    }, 2000);
  };

  return { messages, isTyping, sendMessage };
};
