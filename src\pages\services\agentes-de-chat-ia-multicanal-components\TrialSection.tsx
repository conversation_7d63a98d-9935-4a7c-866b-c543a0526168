import React, { useState } from 'react';

const TrialSection = () => {
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setMessage('');
    if (email && code) {
      // Simulate API call
      setMessage(
        '¡Gracias por activar tu prueba de 14 días! Revisa tu correo para continuar.'
      );
      setEmail('');
      setCode('');
    } else {
      setError('Por favor, introduce un email y un código válidos.');
    }
  };

  return (
    <section id='trial' className='mt-16'>
      <div className='p-8 bg-slate-900 text-white rounded-lg shadow-inner-glow shadow-inner-glow-white text-center'>
        <h2 className='text-3xl font-bold mb-4'>
          Activa tu Prueba Gratuita de 14 Días
        </h2>
        <p className='mb-6 text-slate-50 max-w-2xl mx-auto'>
          Introduce tu correo y el código de activación que has recibido para
          empezar a transformar tu comunicación.
        </p>
        <form onSubmit={handleSubmit} className='max-w-md mx-auto'>
          <div className='mb-4'>
            <input
              type='email'
              placeholder='Tu correo electrónico'
              value={email}
              onChange={e => setEmail(e.target.value)}
              className='w-full px-4 py-3 bg-slate-800 border border-slate-600 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-slate-400'
              required
            />
          </div>
          <div className='mb-4'>
            <input
              type='text'
              placeholder='Tu código de activación'
              value={code}
              onChange={e => setCode(e.target.value)}
              className='w-full px-4 py-3 bg-slate-800 border border-slate-600 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-slate-400'
              required
            />
          </div>
          <button
            type='submit'
            className='w-full inline-block bg-gradient-to-r from-blue-800 via-blue-700 to-blue-800 text-white hover:bg-white hover:text-blue-700
                    hover:text-shadow-glow-blue-700 drop-shadow-glow-white-
                    hover:bg-white
                    shadow-inner-glow shadow-inner-glow-white-md
                    font-bold mt-4 py-3 px-6 rounded-full transition-all duration-300 text-lg'
          >
            Activar Prueba
          </button>
        </form>
        {message && <p className='mt-4 text-lg text-green-400'>{message}</p>}
        {error && <p className='mt-4 text-lg text-red-400'>{error}</p>}
      </div>
    </section>
  );
};

export default TrialSection;
