import React from 'react';
import HeroSectionAgentesDeChat from './agentes-de-chat-ia-multicanal-components/HeroSectionAgentesDeChat';
import KeyBenefitsSection from './agentes-de-chat-ia-multicanal-components/KeyBenefitsSection';
import CommonUseCasesSection from './agentes-de-chat-ia-multicanal-components/CommonUseCasesSection';
import FinalCTASection from './agentes-de-chat-ia-multicanal-components/FinalCTASection';
import TrialSection from './agentes-de-chat-ia-multicanal-components/TrialSection';
import FAQSection from '../../components/FAQSection';
import ChatMockupSection from './agentes-de-chat-ia-multicanal-components/ChatMockupSection';
import CompetitorComparisonSection from './agentes-de-chat-ia-multicanal-components/CompetitorComparisonSection';

const AgentesDeChatIAMulticanal = () => {
  const faqs = [
    {
      question: '¿Qué es un agente de chat IA multicanal?',
      answer:
        'Un agente de chat IA multicanal es un sistema de inteligencia artificial que puede interactuar con clientes a través de múltiples canales de comunicación, como WhatsApp, Telegram, Instagram, Facebook Messenger, y la web. Está diseñado para automatizar respuestas, resolver consultas y mejorar la experiencia del cliente.',
      highlightPhrase: '',
    },
    {
      question: '¿Cómo puede un agente de chat IA beneficiar a mi negocio?',
      answer:
        'Un agente de chat IA puede mejorar la eficiencia al manejar un gran volumen de consultas simultáneamente, reducir los tiempos de espera, proporcionar soporte 24/7, y liberar al personal humano para tareas más complejas. Esto se traduce en una mayor satisfacción del cliente y una reducción de costos operativos.',
      highlightPhrase: '',
    },
    {
      question:
        '¿Es difícil integrar un agente de chat IA en mis sistemas existentes?',
      answer:
        'Nuestros agentes de chat IA están diseñados para una integración sencilla con las plataformas de comunicación más populares y sistemas CRM. Ofrecemos soporte completo durante el proceso de implementación para asegurar una transición fluida y sin interrupciones.',
      highlightPhrase: '',
    },
    {
      question: '¿Qué tipo de consultas puede manejar un agente de chat IA?',
      answer:
        'Nuestros agentes de chat IA pueden manejar una amplia gama de consultas, desde preguntas frecuentes y soporte técnico básico hasta la gestión de reservas, seguimiento de pedidos, y personalización de ofertas. Su capacidad de aprendizaje continuo les permite adaptarse y mejorar con el tiempo.',
      highlightPhrase: '',
    },
    {
      question:
        '¿Cómo se asegura la privacidad y seguridad de los datos con un agente de chat IA?',
      answer:
        'La privacidad y seguridad de los datos son nuestras máximas prioridades. Implementamos estrictas medidas de seguridad, incluyendo cifrado de datos, cumplimiento de normativas de protección de datos (como GDPR y CCPA), y auditorías regulares para garantizar que toda la información del cliente esté protegida.',
      highlightPhrase: '',
    },
  ];

  return (
    <div className='bg-white'>
      <HeroSectionAgentesDeChat />

      <main className='iphone-xr-pro-max:py-2 bg-gradient-to-b from-purple-800 from-[150px] via-white via-[300px] to-white shadow-inner-glow shadow-inner-glow-white-left-2xl shadow-inner-glow-white-right-2xl'>
        <ChatMockupSection />
        <CompetitorComparisonSection />
        <CommonUseCasesSection />
        <div className='max-w-7xl mx-auto px-6 sm:px-6lg:px-8 pb-8 bg-white rounded-t-xl drop-shadow-glow-white-md'>
          <FinalCTASection />
        </div>
        <FAQSection
          faqs={faqs}
          shadowColorClass='drop-shadow-glow-purple-200-bottom-md shadow-inner-glow shadow-inner-glow-pink-100-md'
        />
      </main>
    </div>
  );
};

export default AgentesDeChatIAMulticanal;
