// src/constants/services.ts

import {
  faComment,
  faMicrophone,
  faRobot,
  faEnvelope,
  faCode,
} from '@fortawesome/free-solid-svg-icons';
export const services = [
  {
    id: 'agentes-de-chat-ia',
    title: 'Agentes de Chat IA Multicanal',
    description:
      'Automatiza la comunicación con tus clientes 24/7 con chatbots inteligentes y personalizados.',
    Icon: faComment,
    emoji: '💬',
    features: [
      'Atención al cliente instantánea',
      'Generación y cualificación de leads',
      'Integración multicanal (web, WhatsApp, redes sociales)',
    ],
    link: '/servicios/agentes-de-chat-ia',
    cta: 'Implementar Chat IA',
    glow: 'purple-500',
    backgroundGradient:
      'bg-gradient-to-b from-white via-purple-700/10  via-70% to-purple-700 shadow-inner-glow shadow-inner-glow-white-lg',
    textColor:
      'text-radial-gradient-from-pink-600-to-purple-700 drop-shadow-glow-white',
    bulletGradient: 'from-purple-700 to-pink-500',
    buttonClass: 'cta-button-purple drop-shadow-glow-white-sm',
    pageConfig: {
      servicios: {
        visualsConfig: {
          showEmoji: false,
          showIcon: true,
        },
      },
      home: {
        visualsConfig: {
          showEmoji: true,
          showIcon: true,
        },
      },
    },
  },
  {
    id: 'agentes-de-voz-ia',
    title: 'Agentes de Voz IA',
    description:
      'Ofrece una experiencia de atención telefónica superior y automatizada con IA conversacional.',
    Icon: faMicrophone,
    emoji: '🎙️',
    features: [
      'Interacción natural y fluida',
      'Automatización de llamadas 24/7',
      'Reducción de tiempos de espera y costos operativos',
    ],
    link: '/servicios/agentes-de-voz-ia',
    cta: 'Implementar Voz IA',
    glow: 'green-500',
    backgroundGradient:
      'bg-gradient-to-b from-white via-green-800/10 via-70% to-green-800 shadow-inner-glow shadow-inner-glow-white-lg',
    textColor:
      'text-radial-gradient-from-green-600-to-green-700 drop-shadow-glow-white',
    bulletGradient: 'from-forest-green-700 to-teal-500',
    buttonClass: 'cta-button-green drop-shadow-glow-white-sm',
    pageConfig: {
      servicios: {
        visualsConfig: {
          showEmoji: false,
          showIcon: true,
        },
      },
      home: {
        visualsConfig: {
          showEmoji: true,
          showIcon: true,
        },
      },
    },
  },
  {
    id: 'automatizacion-inteligente',
    title: 'Automatización Inteligente',
    description:
      'Optimización de flujos de trabajo y procesos empresariales mediante la automatización con IA.',
    Icon: faRobot,
    emoji: '🤖',
    features: [
      'Reducción de tareas manuales',
      'Integración con sistemas existentes',
      'Análisis de datos para optimización',
    ],
    link: '/servicios/automatizacion-inteligente',
    cta: 'Automatizar Procesos',
    glow: 'blue-500',
    backgroundGradient:
      'bg-gradient-to-b from-white via-blue-750/10 via-70% to-blue-750 shadow-inner-glow shadow-inner-glow-white-lg',
    textColor:
      'text-radial-gradient-from-blue-600-to-blue-700 drop-shadow-glow-white',
    bulletGradient: 'from-blue-700 to-cyan-500',
    buttonClass: 'cta-button-blue drop-shadow-glow-white-sm',
    pageConfig: {
      servicios: {
        visualsConfig: {
          showEmoji: false,
          showIcon: true,
        },
      },
      home: {
        visualsConfig: {
          showEmoji: true,
          showIcon: true,
        },
      },
    },
  },

  {
    id: 'gestion-inteligente-de-correo',
    title: 'Gestión Inteligente de Correo',
    description:
      'Clasificación y respuesta automática de correos electrónicos para una gestión eficiente.',
    Icon: faEnvelope,
    emoji: '📧',
    features: [
      'Filtro y priorización de emails',
      'Generación de respuestas automáticas',
      'Extracción de información clave',
    ],
    link: '/servicios/gestion-inteligente-de-correo',
    cta: 'Optimizar Email',
    glow: 'indigo-500',
    backgroundGradient:
      'bg-gradient-to-b from-white via-indigo-800/10 via-70% to-indigo-800 shadow-inner-glow shadow-inner-glow-white-lg',
    textColor: 'text-indigo-600 drop-shadow-glow-white',
    bulletGradient: 'from-indigo-700 to-violet-500',
    buttonClass: 'cta-button-indigo drop-shadow-glow-white-sm',
    pageConfig: {
      servicios: {
        visualsConfig: {
          showEmoji: false,
          showIcon: true,
        },
        home: {
          visualsConfig: {
            showEmoji: true,
            showIcon: true,
          },
        },
      },
    },
  },
  {
    id: 'webs-con-ia-integrada',
    title: 'Webs con IA Integrada',
    description:
      'Desarrollo de sitios web modernos con funcionalidades de inteligencia artificial incorporadas.',
    Icon: faCode,
    emoji: '💻',
    features: [
      'Experiencias de usuario personalizadas',
      'Recomendaciones de contenido inteligentes',
      'Análisis de comportamiento del usuario',
    ],
    link: '/servicios/webs-con-ia-integrada',
    cta: 'Desarrollar Web IA',
    glow: 'amberent-500',
    backgroundGradient:
      'bg-gradient-to-b from-white via-amberent-700/10 via-70% to-amberent-700 shadow-inner-glow shadow-inner-glow-white-lg',
    textColor:
      'text-radial-gradient-from-amberent-600-to-amberent-700 drop-shadow-glow-white',
    bulletGradient: 'from-amberent-700 to-yellow-500',
    buttonClass: 'cta-button-amberent drop-shadow-glow-white-sm',
    pageConfig: {
      servicios: {
        visualsConfig: {
          showEmoji: false,
          showIcon: true,
        },
      },
      home: {
        visualsConfig: {
          showEmoji: true,
          showIcon: true,
        },
      },
    },
  },
];
