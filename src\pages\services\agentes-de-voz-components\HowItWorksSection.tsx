import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMicrophoneAlt } from '@fortawesome/free-solid-svg-icons';

const HowItWorksSection = () => {
  return (
    <section className='my-12 py-12 bg-green-100 rounded-lg shadow-inner-glow min-h-screen flex items-center justify-center'>
      <div className='max-w-4xl mx-auto text-center px-4'>
        <h2 className='text-3xl font-bold text-green-800 mb-6'>
          ¿Cómo Funciona Nuestro Asistente de Voz IA?
        </h2>
        <p className='text-lg text-gray-700 mb-8'>
          Nuestro Agente de IA se conecta a tu herramienta de voz, entiende,
          gestiona y mejora la eficiencia de tu negocio.
        </p>
        <div className='bg-white p-8 md:p-12 rounded-2xl border border-gray-300 mb-16'>
          <h3 className='text-2xl md:text-3xl font-bold text-center mb-12'>
            Arquitectura del Sistema
          </h3>
          <div className='flex flex-col md:flex-row items-center justify-center gap-4 md:gap-2'>
            {/* Component 1: Agente de IA */}
            <div className='bg-gray-100 text-center p-6 rounded-lg w-48'>
              <FontAwesomeIcon
                icon={faMicrophoneAlt}
                className='w-10 h-10 mx-auto mb-2 text-green-400'
              />
              <p className='font-bold'>Agente de IA</p>
              <p className='text-sm text-gray-600'>
                (conectado a tu herramienta de voz)
              </p>
            </div>
            {/* Arrows */}
            <div className='flex md:flex-col items-center justify-center mx-2 my-4 md:my-0 md:mx-0'>
              <div className='text-center'>
                <div className='text-2xl transform -rotate-90 md:rotate-0'>
                  →
                </div>
                <p className='text-xs text-gray-600 my-1 md:my-0 md:mx-2'>
                  Tools
                </p>
                <div className='text-2xl transform rotate-90 md:rotate-180'>
                  →
                </div>
              </div>
            </div>
            {/* Component 2: Backend */}
            <div className='relative bg-gray-100 text-center p-6 rounded-lg w-48'>
              <svg
                className='w-10 h-10 mx-auto mb-2 text-purple-400'
                xmlns='http://www.w3.org/2000/svg'
                fill='none'
                viewBox='0 0 24 24'
                strokeWidth='1.5'
                stroke='currentColor'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  d='M21.75 17.25v-.228a4.5 4.5 0 00-.12-1.03l-2.268-9.64a3.375 3.375 0 00-3.285-2.602H7.923a3.375 3.375 0 00-3.285 2.602l-2.268 9.64a4.5 4.5 0 00-.12 1.03v.228m19.5 0a3 3 0 01-3 3H5.25a3 3 0 01-3-3m19.5 0a3 3 0 00-3-3H5.25a3 3 0 00-3 3m16.5 0h.008v.008h-.008v-.008zm-3 0h.008v.008h-.008v-.008z'
                />
              </svg>
              <p className='font-bold'>API Backend</p>
              <p className='text-sm text-gray-600'>Lógica y Datos</p>
              <div className='absolute -bottom-10 left-1/2 -translate-x-1/2 flex items-center gap-2'>
                <div className='text-xl transform rotate-90'>↓</div>
                <div className='flex items-center gap-2 text-xs bg-gray-200 p-1 rounded'>
                  <svg
                    className='w-4 h-4 text-yellow-400'
                    xmlns='http://www.w3.org/2000/svg'
                    fill='none'
                    viewBox='0 0 24 24'
                    strokeWidth='1.5'
                    stroke='currentColor'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      d='M4.034 7.5h15.932c.828 0 1.5.672 1.5 1.5v9c0 .828-.672 1.5-1.5 1.5H4.034a1.5 1.5 0 01-1.5-1.5v-9c0-.828.672-1.5 1.5-1.5zM4.034 7.5V6a1.5 1.5 0 011.5-1.5h1.5a1.5 1.5 0 011.5 1.5V7.5m6.75 0V6a1.5 1.5 0 011.5-1.5h1.5a1.5 1.5 0 011.5 1.5V7.5'
                    />
                  </svg>
                  <span>Bases de Datos</span>
                </div>
              </div>
            </div>
            <div className='text-2xl mt-12 md:mt-0 transform md:rotate-0 rotate-90'>
              →
            </div>
            {/* Component 3: WhatsApp */}
            <div className='bg-gray-100 text-center p-6 rounded-lg w-48 mt-12 md:mt-0'>
              <svg
                className='w-10 h-10 mx-auto mb-2 text-green-500'
                xmlns='http://www.w3.org/2000/svg'
                viewBox='0 0 24 24'
                fill='currentColor'
              >
                <path d='M16.6 14c-.2-.1-1.5-.7-1.7-.8-.2-.1-.4-.1-.6.1-.2.2-.6.7-.8.9-.1.1-.3.2-.5.1-.2-.1-1-.4-1.9-1.2-.7-.6-1.2-1.4-1.3-1.6-.1-.2 0-.4.1-.5.1-.1.2-.2.4-.4.1-.1.2-.2.2-.4.1-.1 0-.3-.1-.4-.1-.1-.6-1.4-.8-1.9-.2-.5-.4-.4-.5-.4h-.5c-.2 0-.4.1-.6.3-.2.2-.8.8-.8 1.9s.8 2.2 1 2.4c.1.1 1.5 2.3 3.7 3.2.5.2.9.4 1.2.5.5.2 1 .1 1.4-.1.4-.2 1.5-1.7 1.7-1.9.2-.2.2-.4.1-.5l-.3-.1zM12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8z' />
              </svg>
              <p className='font-bold'>WhatsApp</p>
              <p className='text-sm text-gray-600'>Envío Final</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection;
