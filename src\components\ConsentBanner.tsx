import React, { useEffect, useState } from 'react';

const ConsentBanner = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const consent = localStorage.getItem('google_consent');
    if (consent === null) {
      setIsVisible(true);
    } else if (consent === 'granted') {
      // If consent was previously granted, update gtag immediately
      if (window.updateGoogleConsent) {
        window.updateGoogleConsent(true);
      }
    }
  }, []);

  const handleAccept = () => {
    if (window.updateGoogleConsent) {
      window.updateGoogleConsent(true);
    }
    localStorage.setItem('google_consent', 'granted');
    setIsVisible(false);
  };

  const handleReject = () => {
    if (window.updateGoogleConsent) {
      window.updateGoogleConsent(false);
    }
    localStorage.setItem('google_consent', 'denied');
    setIsVisible(false);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className='fixed bottom-0 left-0 right-0 bg-white text-gray-800 p-4 flex flex-col sm:flex-row items-center justify-between shadow-lg z-50 border-t border-gray-200'>
      <p className='text-sm mb-2 sm:mb-0 text-center sm:text-left'>
        Utilizamos cookies propias y de terceros para mejorar nuestros servicios
        y mostrarte publicidad relacionada con tus preferencias mediante el
        análisis de tus hábitos de navegación. Si continúas navegando,
        consideramos que aceptas su uso. Puedes cambiar la configuración u
        obtener más información en nuestra{' '}
        <a
          href='/politica-de-cookies'
          className='text-blue-600 hover:underline'
        >
          Política de Cookies
        </a>
        .
      </p>
      <div className='flex space-x-2 mt-2 sm:mt-0'>
        <button
          onClick={handleAccept}
          className='bg-gradient-to-r from-purple-600 to-pink-500 text-white px-6 py-3 rounded-full text-base font-semibold transition-all duration-300 ease-in-out shadow-lg border border-[1px] border-purple-600 hover:bg-blue-700 hover:border-white hover:shadow-inner-glow hover:shadow-inner-glow-white-sm hover:text-shadow-glow-white'
        >
          Aceptar
        </button>
        <button
          onClick={handleReject}
          className='bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-3 rounded-full text-base font-semibold transition-colors duration-200 shadow-md'
        >
          Rechazar
        </button>
      </div>
    </div>
  );
};

export default ConsentBanner;
