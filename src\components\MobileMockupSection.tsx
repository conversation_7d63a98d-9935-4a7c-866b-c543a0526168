import React, { useRef, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import InstagramChatMockup from './InstagramChatMockup';
import PhoneCallMockup from './PhoneCallMockup';

const MobileMockupSection: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const [startAnimation, setStartAnimation] = useState(false);

  useEffect(() => {
    const currentSectionRef = sectionRef.current; // Capture the current value

    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setStartAnimation(true);
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 } // Trigger when 10% of the section is visible
    );

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  return (
    <section ref={sectionRef} className='py-8 relative z-10 overflow-x-hidden'>
      {/*
        NOTA DE SEGURIDAD Y LAYOUT:
        'overflow-x-hidden' se añade aquí para contener el desbordamiento horizontal
        causado por los mockups de Instagram y Phone Call dentro de esta sección,
        especialmente en dispositivos móviles, evitando que afecte el scroll global.
      */}
      <div className='mb-12 py-8 max-w-screen rounded-lg bg-gradient-to-b from-white via-malva-100 to-white'>
        <h2 className='text-3xl font-extrabold text-gray-900  text-shadow-glow-white-md drop-shadow-glow-white-md text-center mb-3'>
          Chatbot de Instagram
        </h2>
        <p className='text-base text-gray-800 text-shadow-glow-white-md drop-shadow-glow-white-md text-center mb-6 z'>
          Automatiza tus respuestas en Instagram con un chatbot inteligente que
          gestiona consultas y ventas 24/7.
        </p>
        <Link to='/servicios/agentes-de-chat-ia'>
          <InstagramChatMockup
            startMessagesAnimation={startAnimation}
            isMobile={true}
          />
        </Link>
        <div className='mt-10 text-center'>
          <Link
            to='/servicios/agentes-de-chat-ia'
            className='inline-block bg-pink-600 text-white text-lg font-semibold py-3 px-8 rounded-full shadow-lg hover:bg-pink-700 transition duration-300 shadow-inner-glow shadow-inner-glow-white-md
            hover:shadow-inner-glow-white-2xl hover:text-shadow-glow-black-sm'
          >
            Ver Demo Chatbot
          </Link>
        </div>
      </div>
      <div className='mt-16 bg-gradient-to-b from-white via-forest-green-100 to-white'>
        <h2 className='text-3xl font-extrabold text-gray-900 text-shadow-glow-white-md drop-shadow-glow-white-md  text-center mb-3'>
          Asistente de Voz para Llamadas
        </h2>
        <p className='text-base text-gray-800 text-center text-shadow-glow-white-md drop-shadow-glow-white-md  mb-6'>
          Un asistente de voz con IA que atiende llamadas, toma reservas y
          resuelve dudas, liberando a tu equipo.
        </p>
        <Link to='/servicios/agentes-de-voz-ia'>
          <PhoneCallMockup
            startMessagesAnimation={startAnimation}
            showChatContent={startAnimation}
            isMobile={true}
          />
        </Link>
        <div className='mt-10 text-center'>
          <Link
            to='/servicios/agentes-de-voz-ia'
            className='inline-block bg-green-600 text-white text-lg font-semibold py-3 px-8 rounded-full shadow-lg hover:bg-green-700 transition duration-300 shadow-inner-glow shadow-inner-glow-white-md
            hover:shadow-inner-glow-white-2xl hover:text-shadow-glow-black-sm'
          >
            Ver Demo Asistente de Voz
          </Link>
        </div>
      </div>
    </section>
  );
};

export default MobileMockupSection;
