import React, { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { gsap } from 'gsap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faComment, faPaperPlane } from '@fortawesome/free-solid-svg-icons';

import { getCloudImageUrl } from '@/utils/gcs';
import HuggingFaceIcon from '../../../components/HuggingFaceIcon';

interface Message {
  id: number;
  text?: string;
  sender: 'user' | 'commerce';
  imageUrl?: string;
  isLink?: boolean;
}

const initialMessages: Message[] = [
  {
    id: 1,
    text: '<PERSON><PERSON>, ¿vuestros agentes de chat funcionan en varias plataformas?',
    sender: 'user',
  },
  {
    id: 2,
    text: '¡Hola! Sí, nuestros agentes se integran donde estén tus clientes.',
    sender: 'commerce',
  },
  {
    id: 3,
    sender: 'commerce',
    imageUrl: 'package_chat_integrations.webp',
  },
  {
    id: 4,
    text: 'Genial, necesito automatizar la atención en Instagram y WhatsApp.',
    sender: 'user',
  },
  {
    id: 5,
    text: 'Y si es posible, centralizar los pedidos.',
    sender: 'user',
  },
  {
    id: 6,
    text: 'Hecho. Podemos unificar la comunicación y gestionar pedidos en ambos canales.',
    sender: 'commerce',
  },
  {
    id: 7,
    text: '¿Agendamos una auditoría gratuita y te enseñamos cómo funciona?',
    sender: 'commerce',
  },
  {
    id: 8,
    text: '¡Claro!',
    sender: 'user',
  },
  {
    id: 9,
    text: 'Perfecto. Agenda tu demo gratuita aquí.',
    sender: 'commerce',
    isLink: true,
  },
];

const ChatMockupSection = () => {
  const [chatMessages, setChatMessages] = useState<Message[]>(initialMessages);
  const [inputText, setInputText] = useState<string>('');
  const messageRefs = useRef<(HTMLDivElement | null)[]>([]);
  const chatBodyRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const duration = 0.5;
    const stagger = 0.25;

    const tl = gsap.timeline({
      delay: 0.5,
      onComplete: () => {
        if (chatBodyRef.current) {
          chatBodyRef.current.scrollTop = chatBodyRef.current.scrollHeight;
        }
      },
    });
    initialMessages.forEach((message, index) => {
      tl.fromTo(
        messageRefs.current[index],
        { opacity: 0, y: 20, scale: 0.95 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: duration,
          ease: 'power2.out',
        },
        `+=${index === 0 ? 0 : stagger}`
      );
    });
  }, []); // Run only once on mount for initial animation

  useEffect(() => {
    // Animate only newly added messages
    const newMessagesStartIndex = initialMessages.length;
    if (chatMessages.length > newMessagesStartIndex) {
      const lastMessageIndex = chatMessages.length - 1;
      const messageElement = messageRefs.current[lastMessageIndex];
      if (messageElement) {
        gsap.fromTo(
          messageElement,
          { opacity: 0, y: 20, scale: 0.95 },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.5,
            ease: 'power2.out',
          }
        );
      }
    }

    // Scroll to bottom on new messages
    if (chatBodyRef.current) {
      chatBodyRef.current.scrollTop = chatBodyRef.current.scrollHeight;
    }
  }, [chatMessages]); // Run whenever chatMessages changes

  const simulateApiResponse = (userMessage: string): Promise<string> => {
    return new Promise(resolve => {
      setTimeout(() => {
        let response = 'Lo siento, no entendí tu pregunta.';
        const lowerCaseMessage = userMessage.toLowerCase();

        if (lowerCaseMessage.includes('hola')) {
          response = '¡Hola! ¿En qué puedo ayudarte hoy?';
        } else if (
          lowerCaseMessage.includes('funciona') ||
          lowerCaseMessage.includes('plataformas')
        ) {
          response =
            'Nuestros agentes de chat IA funcionan en múltiples plataformas como WhatsApp, Instagram, Facebook Messenger y tu sitio web.';
        } else if (
          lowerCaseMessage.includes('precios') ||
          lowerCaseMessage.includes('costo')
        ) {
          response =
            'Para información sobre precios, por favor agenda una demo gratuita con uno de nuestros expertos.';
        } else if (
          lowerCaseMessage.includes('contacto') ||
          lowerCaseMessage.includes('auditoría')
        ) {
          response =
            'Puedes agendar una demo gratuita en nuestra sección de contacto o haciendo clic en el botón de abajo.';
        } else if (lowerCaseMessage.includes('gracias')) {
          response = 'De nada. ¡Estoy aquí para ayudarte!';
        }

        resolve(response);
      }, 1000); // Simulate network delay
    });
  };

  const handleSendMessage = async () => {
    if (inputText.trim() === '') return;

    const newUserMessage: Message = {
      id: chatMessages.length + 1,
      text: inputText.trim(),
      sender: 'user',
    };

    setChatMessages(prevMessages => [...prevMessages, newUserMessage]);
    setInputText('');

    // Simulate API response
    const apiResponseText = await simulateApiResponse(
      newUserMessage.text || ''
    );
    const apiResponseMessage: Message = {
      id: chatMessages.length + 2,
      text: apiResponseText,
      sender: 'commerce',
    };

    setChatMessages(prevMessages => [...prevMessages, apiResponseMessage]);
  };

  return (
    <section className='overflow-hidden py-8 md:max-w-7xl mx-auto px-6 sm:px-6 lg:px-6 bg-white rounded-t-xl drop-shadow-glow-white-md'>
      <div
        className='flex flex-col lg:flex-row items-center lg:items-start justify-center lg:justify-between md:max-w-7xl mx-auto px-6 sm:px-6 lg:px-8 py-8
      bg-white rounded-lg drop-shadow-glow-white-lg'
      >
        <div className='lg:w-2/2 mb-8 lg:mb-0 lg:pr-8'>
          <h2 className='text-4xl font-bold text-gray-900 mb-6'>
            Interactúa con nuestro Agente de Chat IA
          </h2>
          <p className='text-lg text-gray-700 mb-4'>
            Experimenta de primera mano cómo nuestros agentes de chat IA pueden
            transformar la comunicación con tus clientes. Este simulador
            interactivo te permite ver la fluidez y eficiencia de una
            conversación automatizada.
          </p>
          <p className='text-lg text-gray-700 mb-4'>
            Observa cómo el agente responde preguntas, guía al usuario y maneja
            diferentes escenarios, todo en tiempo real y sin intervención
            humana. Es una demostración clara de la capacidad de nuestros
            sistemas para ofrecer soporte 24/7 y mejorar la experiencia del
            cliente.
          </p>
          <p className='text-lg text-gray-700'>
            ¡Imagina el impacto en tu negocio al automatizar estas
            interacciones! Reduce costos, aumenta la satisfacción del cliente y
            libera a tu equipo para tareas más estratégicas.
          </p>
        </div>
        <div className='lg:w-2/2 flex justify-center lg:justify-end'>
          <div
            className='relative sm:max-w-sm bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-200 border-4 transform transition-transform duration-500 drop-shadow-glow-pink-600-md'
            style={{ aspectRatio: '9 / 16', height: '600px' }} // Instagram story/reel aspect ratio, fixed height for scroll
          >
            {/* Header */}
            <div className='flex items-center justify-between p-3 bg-gradient-to-t from-purple-600 to-pink-600 text-white shadow-md'>
              <div className='flex items-center space-x-3 px-2'>
                <div className='w-8 h-8 bg-white rounded-full flex items-center justify-center text-purple-600 font-bold'>
                  IA
                </div>
                <span className='text-lg font-semibold text-md text-shadow-glow-black-xl text-center flex items-center'>
                  <HuggingFaceIcon className='mr-2' height='2em' width='2em' />
                  Agente de Chat IA
                </span>
              </div>
              <FontAwesomeIcon icon={faComment} className='w-5 h-5 mr-2' />
            </div>

            {/* Chat Body */}
            <div
              ref={chatBodyRef}
              className='flex-1 p-3 overflow-y-auto thin-purple-scrollbar'
              style={{ height: 'calc(100% - 120px)' }} // Adjusted height for input
              data-nosnippet='true'
            >
              {chatMessages.map((message, index) => (
                <div
                  key={message.id}
                  ref={el => (messageRefs.current[index] = el)}
                  className={`mb-2 flex ${
                    message.sender === 'user' ? 'justify-end' : 'justify-start'
                  }`}
                  // style={{ opacity: 0 }} // Initially hidden
                >
                  {message.text ? (
                    message.isLink ? (
                      <Link
                        to='/demo-gratuita'
                        className={`max-w-[85%] px-3 py-1.5 rounded-xl text-sm underline ${
                          message.sender === 'user'
                            ? 'bg-instagram-gradient-messages text-white rounded-br-none'
                            : 'bg-gray-200 text-gray-800 rounded-bl-none'
                        }`}
                      >
                        {message.text}
                      </Link>
                    ) : (
                      <div
                        className={`max-w-[85%] px-3 py-1.5 rounded-xl text-sm ${
                          message.sender === 'user'
                            ? 'bg-instagram-gradient-messages text-white rounded-br-none'
                            : 'bg-gray-200 text-gray-800 rounded-bl-none'
                        }`}
                      >
                        {message.text}
                      </div>
                    )
                  ) : message.imageUrl ? (
                    <div className='max-w-[60%]'>
                      <img
                        src={getCloudImageUrl(message.imageUrl)}
                        alt='Integraciones de chat'
                        className='rounded-xl'
                      />
                    </div>
                  ) : null}
                </div>
              ))}
            </div>

            {/* Input */}
            <div className='p-2 border-t border-gray-200 flex items-center mb-2 sm:mb-0'>
              <input
                type='text'
                className='flex-1 border rounded-full px-3 py-1.5 mr-1 focus:outline-none focus:ring-2 focus:ring-purple-500'
                placeholder='Escribe un mensaje...'
                value={inputText}
                onChange={e => setInputText(e.target.value)}
                onKeyPress={e => {
                  if (e.key === 'Enter') {
                    handleSendMessage();
                  }
                }}
              />
              <button
                className='bg-purple-600 text-white rounded-full p-2 hover:bg-purple-700 transition duration-300'
                onClick={handleSendMessage}
              >
                <FontAwesomeIcon icon={faPaperPlane} className='w-6 h-4' />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ChatMockupSection;
