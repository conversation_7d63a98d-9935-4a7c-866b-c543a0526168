import { useState, ChangeEvent, useEffect, useRef, KeyboardEvent } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCalendarAlt } from '@fortawesome/free-solid-svg-icons';
import './../components/BorderAnimation.css';
import FondoAnimadoBurbujas from '../components/FondoAnimadoBurbujas';
import { Link } from 'react-router-dom';

function useIsMobile(breakpoint = 768) {
  const [isMobile, setIsMobile] = useState(window.innerWidth < breakpoint);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < breakpoint);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [breakpoint]);

  return isMobile;
}

function ConsultaGratuita() {
  const calendarRef = useRef<HTMLDivElement>(null);
  const scheduleLinkRef = useRef<HTMLAnchorElement>(null);
  const scheduleButtonRef = useRef<HTMLButtonElement>(null);
  const [calendarEmail, setCalendarEmail] = useState('<EMAIL>');
  const [isCalendarEmailValid, setIsCalendarEmailValid] = useState(true);
  const [showCalendar, setShowCalendar] = useState(false);
  const isMobile = useIsMobile();

  useEffect(() => {
    if (showCalendar && calendarRef.current) {
      const topPos =
        calendarRef.current.getBoundingClientRect().top + window.pageYOffset;
      window.scrollTo({
        top: topPos - 200,
        behavior: 'smooth',
      });
    }
  }, [showCalendar]);

  const handleCalendarEmailChange = (e: ChangeEvent<HTMLInputElement>) => {
    const email = e.target.value;
    setCalendarEmail(email);
    const corporateEmailRegex =
      /^[a-zA-Z0-9._%+-]+@(?!gmail\.com|hotmail\.com|yahoo\.com|outlook\.com)[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    setIsCalendarEmailValid(corporateEmailRegex.test(email));
  };

  const handleScheduleClick = () => {
    if (isCalendarEmailValid) {
      setShowCalendar(true);
    }
  };

  const handleInputKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    const email = e.currentTarget.value;
    const corporateEmailRegex =
      /^[a-zA-Z0-9._%+-]+@(?!gmail\.com|hotmail\.com|yahoo\.com|outlook\.com)[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const isValid = corporateEmailRegex.test(email);

    if (isValid) {
      if (e.key === 'Enter') {
        e.preventDefault();
        if (isMobile) {
          scheduleLinkRef.current?.click();
        } else {
          handleScheduleClick();
        }
      } else if (e.key === 'Tab' && !e.shiftKey) {
        e.preventDefault();
        if (isMobile) {
          scheduleLinkRef.current?.focus();
        } else {
          scheduleButtonRef.current?.focus();
        }
      }
    }
  };

  const baseInputClass =
    'w-full px-3 py-2 sm:py-2 bg-gray-50 border-2 border-gray-200 rounded-lg focus:outline-none mb-4 drop-shadow-glow-white-sm';

  const calendarUrl = `https://calendar.app.google/iyPPXWKVjEVYAr8eA?prefill_email=${encodeURIComponent(
    calendarEmail
  )}`;

  return (
    <div className='relative isolate'>
      <FondoAnimadoBurbujas />
      <div className='min-h-screen'>
        <div
          className={`relative flex ${showCalendar && !isMobile ? 'items-stretch' : 'items-start'} justify-center ${showCalendar && !isMobile ? 'py-8' : 'pt-32'} px-4 sm:px-6 lg:px-8 overflow-hidden min-h-screen`}
        >
          <div
            className={`relative z-10 flex flex-col w-full ${showCalendar && !isMobile ? 'max-w-full p-0 m-0 drop-shadow-glow-white-lg' : 'max-w-4xl'} mx-auto transition-all duration-500 flex-grow`}
          >
            <div
              className={`${showCalendar && !isMobile ? 'p-0 m-0 drop-shadow-glow-white-lg' : ''} flex flex-col transition-all duration-500`}
            >
              {isMobile || !showCalendar ? (
                <>
                  <div className='flex-grow mb-8 bg-white/0 backdrop-blur-lg p-8 sm:p-12 rounded-3xl shadow-inner-glow shadow-inner-glow-blue-400 drop-shadow-glow-white'>
                    <h2 className='text-4xl font-bold text-radial-gradient-from-blue-600-to-blue-750 drop-shadow-glow-white mb-2 text-center'>
                      Agenda tu Demo Gratuita
                    </h2>
                    <p className='text-center text-gray-900 text-shadow-glow-white drop-shadow-glow-white mb-4 text-base sm:text-base mb-8'>
                      Usa tu correo de empresa para agendar directamente una
                      llamada
                    </p>
                    <div className='space-y-3 max-w-md mx-auto'>
                      <div>
                        <label
                          htmlFor='calendarEmail'
                          className='block text-sm font-semibold text-blue-800 text-shadow-glow-white-sm mb-2'
                        >
                          Correo de Empresa
                        </label>
                        <input
                          type='email'
                          id='calendarEmail'
                          placeholder='<EMAIL>'
                          className={`${baseInputClass} ${
                            isCalendarEmailValid
                              ? 'border-green-700'
                              : 'border-gray-300'
                          }`}
                          onChange={handleCalendarEmailChange}
                          onKeyDown={handleInputKeyDown}
                          value={calendarEmail}
                        />
                      </div>
                      {isMobile ? (
                        <a
                          ref={scheduleLinkRef}
                          href={calendarUrl}
                          target='_blank'
                          rel='noopener noreferrer'
                          className={`group cta-button cta-button-blue w-full animated-border drop-shadow-glow-white mt-2 ${
                            !isCalendarEmailValid &&
                            'opacity-50 cursor-not-allowed'
                          }`}
                          onClick={e => {
                            if (!isCalendarEmailValid) {
                              e.preventDefault();
                            }
                          }}
                        >
                          <span className='relative z-10 flex items-center justify-center space-x-2 text-sm sm:text-base'>
                            <FontAwesomeIcon
                              icon={faCalendarAlt}
                              className='h-5 w-5'
                            />
                            <span>Agendar Cita</span>
                          </span>
                        </a>
                      ) : (
                        <button
                          ref={scheduleButtonRef}
                          type='button'
                          onClick={handleScheduleClick}
                          disabled={!isCalendarEmailValid}
                          className='group cta-button cta-button-blue w-full animated-border drop-shadow-glow-white hover:drop-shadow-glow-blue mt-2 disabled:opacity-50 disabled:cursor-not-allowed'
                        >
                          <span className='relative z-10 flex items-center justify-center space-x-2 text-sm sm:text-base'>
                            <FontAwesomeIcon
                              icon={faCalendarAlt}
                              className='h-5 w-5'
                            />
                            <span>Agendar Cita</span>
                          </span>
                        </button>
                      )}
                    </div>
                  </div>
                  {/* dsdsa {bg-white/50 backdrop-blur-lg p-8 sm:p-12 rounded-3xl flex flex-col
            transition-all duration-500 
            shadow-inner-glow shadow-inner-glow-white-lg
            drop-shadow-glow-white mb-8 md:mb-0 md:w-1/2 md:opacity-100} */}
                  <div
                    className='mb-10 mt-2 bg-white/50 backdrop-blur-lg p-8 sm:p-12 rounded-3xl  shadow-inner-glow shadow-inner-glow-white-lg
            drop-shadow-glow-white-2xl text-center'
                  >
                    <div className='space-y-3 max-w-md mx-auto'>
                      <p className='text-gray-900 text-shadow-glow-white drop-shadow-glow-white mb-4 text-base sm:text-base'>
                        ¿No tienes un correo de empresa o prefieres un contacto
                        más personalizado?
                      </p>
                      <Link
                        to='/contacto'
                        className='group cta-button cta-button-green animated-border drop-shadow-glow-white hover:drop-shadow-glow-green w-full mx-auto'
                      >
                        <span className='relative z-10 flex items-center justify-center space-x-2'>
                          <span className='text-sm sm:text-base'>
                            Contáctanos por correo
                          </span>
                        </span>
                      </Link>
                      <p className='text-gray-900 text-shadow-glow-white drop-shadow-glow-white mt-4 text-sm sm:text-sm'>
                        Te responderemos inmediatamente de manera personalizada
                        gracias a nuestras automatizaciones.
                      </p>
                    </div>
                  </div>
                </>
              ) : null}
              {!isMobile && showCalendar && (
                <div
                  ref={calendarRef}
                  className='flex-grow flex flex-col bg-white rounded-2xl p-4 h-full'
                >
                  <div className='rounded-lg overflow-hidden flex-grow min-h-screen'>
                    <iframe
                      src={calendarUrl}
                      width='100%'
                      height='100%'
                      frameBorder='0'
                      className='rounded-xl'
                    ></iframe>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ConsultaGratuita;
