import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

// 1. Simplificamos los datos: ya no necesitamos un filtro por cada triángulo.
const triangleData = [
  {
    id: 'triangle1',
    fill: 'rgba(17, 77, 17, 0.5)',
    points: [
      [0.3, 0.2],
      [0.1, 0.4],
      [0.4, 0.45],
    ],
  },
  {
    id: 'triangle2',
    fill: 'rgba(40, 199, 40, 0.5)',
    points: [
      [0.75, 0.65],
      [0.9, 0.85],
      [0.65, 0.9],
    ],
  },
  {
    id: 'triangle3',
    fill: 'rgba(19, 100, 19, 0.5)',
    points: [
      [0.5, 0.45],
      [0.45, 0.6],
      [0.55, 0.6],
    ],
  },
  {
    id: 'triangle4',
    fill: 'rgba(144, 238, 144, 0.5)',
    points: [
      [0.1, 0.8],
      [0.2, 0.9],
      [0.05, 0.95],
    ],
  },
  {
    id: 'triangle5',
    fill: 'rgba(144, 238, 144, 0.5)',
    points: [
      [0.8, 0.1],
      [0.95, 0.15],
      [0.85, 0.25],
    ],
  },
  {
    id: 'triangle6',
    fill: 'rgba(144, 238, 144, 0.5)',
    points: [
      [0.6, 0.3],
      [0.7, 0.2],
      [0.75, 0.4],
    ],
  },
];

const AnimatedGreenTrianglesBackground = () => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    const svg = svgRef.current;
    if (!svg) return;

    const { clientWidth: width, clientHeight: height } = svg;

    // 2. Posicionamos cada triángulo como antes.
    triangleData.forEach(data => {
      const triangleElement = svg.querySelector<SVGPolygonElement>(
        `#${data.id}`
      );
      if (!triangleElement) return;

      const pointsStr = data.points
        .map(p => `${p[0] * width},${p[1] * height}`)
        .join(' ');

      triangleElement.setAttribute('points', pointsStr);

      // 3. Animamos cada triángulo individualmente (movimiento, escala, opacidad).
      gsap.to(triangleElement, {
        x: 'random(-200, 200)',
        y: 'random(-200, 200)',
        scale: 'random(2.5, 2.5)',
        opacity: 'random(0.1, 0.4)',
        duration: 'random(10, 20)',
        ease: 'none',
        repeat: -1,
        yoyo: true,
      });
    });

    // 4. Seleccionamos el único elemento de desenfoque y lo animamos.
    //    Esto afectará a todo el grupo de triángulos a la vez.
    const blurElement = svg.querySelector('#blur-effect');
    if (blurElement) {
      gsap.to(blurElement, {
        attr: { stdDeviation: 'random(10, 20)' }, // Aumentamos un poco el blur para que se note en el fondo
        duration: 'random(8, 15)',
        ease: 'power1.inOut',
        repeat: -1,
        yoyo: true,
      });
    }
  }, []);

  return (
    <svg
      ref={svgRef}
      className='absolute top-0 left-0 w-full h-full overflow-hidden z-0 shadow-inner-glow shadow-inner-glow-white-2xl'
    >
      {/* 5. Definimos el filtro de desenfoque UNA SOLA VEZ. */}
      <defs>
        <filter id='background-blur'>
          {/* Le damos un id a feGaussianBlur para poder seleccionarlo fácilmente con GSAP */}
          <feGaussianBlur
            in='SourceGraphic'
            stdDeviation='15'
            id='blur-effect'
          />
        </filter>
      </defs>

      {/* 6. Agrupamos todos los polígonos dentro de una etiqueta <g> 
          y le aplicamos el filtro al grupo. */}
      <g filter='url(#background-blur)'>
        {triangleData.map(data => (
          <polygon
            key={data.id}
            id={data.id}
            fill={data.fill}
            // Ya no se aplica el filtro aquí
          />
        ))}
      </g>
    </svg>
  );
};

export default AnimatedGreenTrianglesBackground;
