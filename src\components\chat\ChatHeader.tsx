import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/free-solid-svg-icons';
import { Link } from 'react-router-dom';
import HuggingFaceIcon from '../HuggingFaceIcon';

interface ChatHeaderProps {
  onClose: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({ onClose }) => {
  return (
    <header className='bg-blue-700 text-white p-4 flex flex-col items-center rounded-t-3xl shadow-inner-glow shadow-inner-glow-white-xl'>
      <div className='w-full text-center mb-2'>
        <Link
          to='/contacto?focus=companyName'
          className='bg-green-500 underline text-white text-xs py-1 px-4 rounded-full shadow-inner-glow shadow-inner-glow-white-md hover:bg-green-600 transition-colors duration-300 text-shadow-glow-black
          hover:shadow-inner-glow-green-600-md hover:text-shadow-glow-white hover:drop-shadow-glow-white-xs hover:font-bold '
          aria-label='Quiero un bot como este'
          onClick={onClose}
        >
          Quiero un bot como este
        </Link>
      </div>
      <div className='flex flex-col items-start w-full px-4'>
        <div className='flex items-center'>
          <HuggingFaceIcon height='3em' width='3em' />
          <h3 className='font-bold text-2xl ml-2'>Asistente IA</h3>
        </div>
        <p className='text-sm mt-1'>
          En línea <span className='font-bold'>24/7</span>
        </p>
      </div>
      <button
        onClick={onClose}
        className='absolute top-4 right-4 w-10 h-10 bg-white rounded-full flex items-center justify-center text-blue-700 shadow-inner-glow shadow-inner-glow-white-md focus:outline-none transform hover:scale-105 
        hover:shadow-inner-glow-blue-700-md hover:bg-white hover:text-blue-800 hover:text-shadow-glow-blue-800 hover:drop-shadow-glow-white-xs'
        aria-label='Cerrar chat'
      >
        <FontAwesomeIcon icon={faTimes} />
      </button>
    </header>
  );
};

export default ChatHeader;
