import React from 'react';
import { GetCloudVideoUrl } from '@/utils/gcs';

export const AsistenteDeVozGoogleCloudVideoEmbed = ({
  className,
}: {
  className?: string;
}) => {
  const videoFileName = '20250616__asistente_de_voz__ejemplo_reservas.mp4';
  const videoSrc = GetCloudVideoUrl(videoFileName);

  return (
    <div className={`rounded-2xl overflow-hidden bg-white ${className}`}>
      <video
        src={videoSrc}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
        }}
        controls
        autoPlay
        muted
        loop
        playsInline
        title='AGENTE DE VOZ PARA RESERVAS - DEMO'
      >
        <track
          src={`/captions/${videoFileName.replace('.mp4', '.vtt')}`}
          kind='captions'
          srcLang='es'
          label='Español'
          default
        />
      </video>
    </div>
  );
};

export default AsistenteDeVozGoogleCloudVideoEmbed;
