import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { services } from '../constants/services';
import ServicePageTemplate from '../components/ServicePageTemplate';

const GestionInteligenteDeCorreo = () => {
  const service = services.find(s => s.id === 'gestion-inteligente-de-correo');
  const location = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  if (!service) {
    return <div>Servicio no encontrado.</div>;
  }

  return (
    <>
      <ServicePageTemplate service={service} />
    </>
  );
};

export default GestionInteligenteDeCorreo;
