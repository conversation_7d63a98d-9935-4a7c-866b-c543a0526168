import React from 'react';
import TableOfContents from '@/components/TableOfContents';
import { getCloudImageUrl } from '@/utils/gcs';
import { blogPosts } from '@/constants/blog';
import { Link } from 'react-router-dom';

const ProtocoloMcpIaPage = () => {
  const sections = [
    {
      id: 'introduccion',
      title: '¿Qué es el Protocolo de Contexto de Modelos (MCP)?',
    },
    {
      id: 'como-funciona',
      title: 'La Arquitectura: Hosts, Clientes y Servidores',
    },
    {
      id: 'mcp-vs-rag',
      title: 'MCP vs. RAG: La Nueva Frontera de la IA Conectada',
    },
    { id: 'beneficios', title: 'Beneficios Estratégicos para tu Negocio' },
    {
      id: 'casos-de-uso',
      title: 'Aplicaciones Reales: ¿Qué Puedes Construir con MCP?',
    },
    {
      id: 'cta-agentes-ia',
      title: 'Transforma tu Negocio con Agentes IA Automatizados',
    },
  ];

  return (
    <>
      {/* Contenedor principal de la página, con fondo base blanco */}
      <div className='bg-white iphone-xr-pro-max:py-4 py-8 '>
        {/* Encabezado del artículo con un degradado sugerido */}
        <header
          className='relative mt-2 sm:pb-6 bg-gradient-to-b from-white via-blue-600 via-70% to-blue-800
         shadow-inner-glow shadow-inner-glow-white-right-2xl shadow-inner-glow-white-left-2xl'
        >
          <div className='relative max-w-7xl mx-auto px-6 sm:px-6 lg:px-8'>
            <h1
              className='text-6xl font-bold tracking-xl font-extrabold tracking-tight
            text-white text-shadow-glow-white-opacity-30 drop-shadow-glow-blue-900-sm'
            >
              MCP: El Protocolo que Desbloquea la Autonomía Real de la IA
            </h1>
            <p className='text-sm text-gray-300 text-shadow-glow-white-opacity-30 drop-shadow-glow-blue-900-lg my-4'>
              Publicado el {blogPosts[0].date}
            </p>
            <p className='mt-2 text-lg md:text-xl mx-auto text-white text-shadow-glow-white-opacity-30 drop-shadow-glow-blue-900-lg'>
              Descubre el Model Context Protocol (MCP), el estándar abierto que
              funciona como un "USB-C para la IA", permitiendo a los agentes
              inteligentes conectarse a cualquier fuente de datos en tiempo
              real.
            </p>
          </div>
        </header>

        {/* Contenedor principal para el contenido */}
        <main
          className='pt-1 bg-gradient-to-b from-blue-800 from-[150px] via-white via-[300px] to-white
         shadow-inner-glow shadow-inner-glow-white-right-2xl shadow-inner-glow-white-left-2xl shadow-inner-glow-white-right-2xl shadow-inner-glow-white-bottom-2xl'
        >
          <div className='max-w-7xl mx-auto px-6 sm:px-6lg:px-8 bg-white rounded-t-xl drop-shadow-glow-white-md pb-8'>
            {/* TOC para móviles */}
            <div className='lg:hidden pt-12'>
              <TableOfContents sections={sections} />
            </div>

            <div className='flex flex-col lg:flex-row gap-x-12 gap-y-16 py-1'>
              {/* Contenido del Blog */}
              <div className='w-full lg:w-3/4'>
                <article className='prose prose-lg max-w-none prose-headings:text-blue-600'>
                  <section id='introduccion'>
                    <h2 className='text-3xl mt-8 mb-6 pb-2 border-b-2 border-blue-500'>
                      ¿Qué es el Protocolo de Contexto de Modelos (MCP)?
                    </h2>
                    <p>
                      El Protocolo de Contexto de Modelos (MCP, por sus siglas
                      en inglés) es un estándar abierto diseñado para
                      estandarizar cómo las aplicaciones de Inteligencia
                      Artificial acceden al contexto que necesitan para operar
                      de forma eficaz. Desarrollado por Anthropic, la empresa
                      detrás del modelo de IA Claude, MCP actúa como un conector
                      universal que permite a los agentes de IA interactuar de
                      forma segura y dinámica con herramientas externas como
                      APIs, bases de datos y aplicaciones empresariales.
                    </p>
                    <figure className='my-6'>
                      <img
                        className='w-full h-auto rounded-xl shadow-lg object-cover'
                        src={getCloudImageUrl(
                          'diagrama_protocolo_mcp_ia_conectada.webp'
                        )}
                        alt='Diagrama del Protocolo MCP conectando una IA a diferentes fuentes de datos'
                      />
                      <figcaption className='text-center text-sm text-gray-500 mt-2'>
                        MCP crea un puente estandarizado entre los modelos de IA
                        y el mundo de los datos en tiempo real.
                      </figcaption>
                    </figure>
                    <blockquote className='border-l-4 border-blue-500 bg-blue-50/50 p-4 my-8 text-lg italic text-slate-700'>
                      "Piensa en MCP como un puerto USB-C para aplicaciones de
                      IA. Así como el USB-C estandariza la conexión de
                      dispositivos a periféricos, MCP unifica la forma en que
                      los modelos de IA se conectan a fuentes de contexto
                      externas."
                    </blockquote>
                    <p>
                      Este protocolo es la pieza clave para la "IA agéntica":
                      programas inteligentes que pueden perseguir objetivos de
                      forma autónoma, yendo más allá de su entrenamiento inicial
                      para tomar decisiones informadas con datos actualizados.
                    </p>
                  </section>

                  <hr className='my-12 h-px border-t-0 bg-transparent bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-25' />

                  <section id='como-funciona'>
                    <h2 className='text-3xl mt-8 mb-6 pb-2 border-b-2 border-blue-500'>
                      La Arquitectura: Hosts, Clientes y Servidores
                    </h2>
                    <p>
                      El funcionamiento de MCP se basa en una arquitectura de
                      tres componentes principales que facilitan la comunicación
                      entre el modelo de IA y las fuentes de datos.
                    </p>
                    <figure className='my-6'>
                      <img
                        className='w-full h-auto rounded-xl shadow-lg object-cover'
                        src={getCloudImageUrl(
                          'mcp_architecture_diagram_v2.webp'
                        )}
                        alt='Diagrama de la arquitectura MCP con Host, Client y Server'
                      />
                      <figcaption className='text-center text-sm text-gray-500 mt-2'>
                        La arquitectura de MCP está diseñada para una
                        comunicación fluida y segura.
                      </figcaption>
                    </figure>
                    <div className='space-y-6 my-8'>
                      <div className='p-4 border border-slate-200 rounded-lg'>
                        <h3 className='text-2xl mt-6 mb-4 font-semibold text-slate-900'>
                          1. MCP Hosts (Anfitriones)
                        </h3>
                        <p className='text-base text-slate-600 mt-1'>
                          Son las aplicaciones que necesitan la información,
                          como un asistente de IA tipo Claude o un agente
                          automatizado desarrollado a medida. El Host inicia la
                          solicitud de contexto.
                        </p>
                      </div>
                      <div className='p-4 border border-slate-200 rounded-lg'>
                        <h3 className='text-2xl mt-6 mb-4 font-semibold text-slate-900'>
                          2. MCP Clients (Clientes)
                        </h3>
                        <p className='text-base text-slate-600 mt-1'>
                          Es el protocolo que gestiona la comunicación entre el
                          Host y el Servidor. Se encarga de formatear la
                          petición y asegurar que la "conversación" entre ambos
                          extremos sea fluida y estandarizada.
                        </p>
                      </div>
                      <div className='p-4 border border-slate-200 rounded-lg'>
                        <h3 className='text-2xl mt-6 mb-4 font-semibold text-slate-900'>
                          3. MCP Servers (Servidores)
                        </h3>
                        <p className='text-base text-slate-600 mt-1'>
                          Este es el componente clave. Es un programa que
                          "expone" las funcionalidades y el acceso a los datos
                          (archivos, bases de datos, APIs). Actúa como un
                          guardián seguro que traduce las peticiones del Host en
                          acciones concretas sobre tus sistemas, sin exponerlos
                          directamente.
                        </p>
                      </div>
                    </div>
                  </section>

                  <hr className='my-12 h-px border-t-0 bg-transparent bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-25' />

                  <section id='mcp-vs-rag'>
                    <h2 className='text-3xl mt-8 mb-6 pb-2 border-b-2 border-blue-500'>
                      MCP vs. RAG: La Nueva Frontera de la IA Conectada
                    </h2>
                    <p>
                      Hasta hace poco, el método más popular para conectar la IA
                      con datos externos era el RAG (Retrieval-Augmented
                      Generation). RAG funciona buscando en bases de datos
                      vectoriales previamente indexadas. MCP ofrece un enfoque
                      más directo y, en muchos casos, más eficiente.
                    </p>
                    <figure className='my-6'>
                      <img
                        className='w-full h-auto rounded-xl shadow-lg object-cover'
                        src={getCloudImageUrl('mcp_vs_rag_comparison.webp')}
                        alt='Comparativa entre MCP y RAG'
                      />
                      <figcaption className='text-center text-sm text-gray-500 mt-2'>
                        MCP y RAG son dos enfoques complementarios para conectar
                        la IA con datos externos.
                      </figcaption>
                    </figure>
                    <blockquote className='border-l-4 border-blue-500 bg-blue-50/50 p-4 my-8 text-lg italic text-slate-700'>
                      "A diferencia de RAG, que requiere generar embeddings y
                      almacenar documentos, un servidor MCP puede acceder
                      directamente a los datos sin indexación previa. Esto se
                      traduce en información más precisa y actualizada con menor
                      carga computacional."
                    </blockquote>
                    <p>
                      Esto no significa que RAG quede obsoleto, sino que MCP
                      proporciona una alternativa poderosa para escenarios donde
                      el acceso en tiempo real a sistemas estructurados y APIs
                      es fundamental. Permite a los agentes de IA interactuar
                      con herramientas, no solo con documentos.
                    </p>
                  </section>

                  <hr className='my-12 h-px border-t-0 bg-transparent bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-25' />

                  <section id='beneficios'>
                    <h2 className='text-3xl mt-8 mb-6 pb-2 border-b-2 border-blue-500'>
                      Beneficios Estratégicos para tu Negocio
                    </h2>
                    <p>
                      Implementar agentes IA basados en MCP no es una mejora
                      técnica, es una revolución operativa.
                    </p>
                    <figure className='my-6'>
                      <img
                        className='w-full h-auto rounded-xl shadow-lg object-cover'
                        src={getCloudImageUrl(
                          'mcp_business_benefits_icons.webp'
                        )}
                        alt='Iconos de beneficios de negocio de MCP'
                      />
                      <figcaption className='text-center text-sm text-gray-500 mt-2'>
                        MCP ofrece beneficios tangibles en interoperabilidad,
                        seguridad y autonomía.
                      </figcaption>
                    </figure>
                    <div className='space-y-4 mt-6'>
                      <h4 className='text-xl mt-4 mb-3 font-bold'>
                        Acceso a Datos en Tiempo Real
                      </h4>
                      <p>
                        Tus agentes tomarán decisiones basadas en la información
                        más reciente de tu CRM, ERP o cualquier otra base de
                        datos, no en datos de entrenamiento obsoletos.
                      </p>
                      <h4 className='text-xl mt-4 mb-3 font-bold'>
                        Interoperabilidad Universal
                      </h4>
                      <p>
                        Conecta cualquier modelo de IA con tus sistemas sin
                        necesidad de desarrollar integraciones a medida para
                        cada combinación. Esto reduce drásticamente el tiempo y
                        el coste de implementación.
                      </p>
                      <h4 className='text-xl mt-4 mb-3 font-bold'>
                        Mayor Seguridad y Control
                      </h4>
                      <p>
                        Los servidores MCP actúan como una capa de seguridad.
                        Los modelos de IA no acceden directamente a tus bases de
                        datos, sino que solicitan acciones al servidor, dándote
                        un control total sobre qué información se comparte y
                        cómo.
                      </p>
                      <h4 className='text-xl mt-4 mb-3 font-bold'>
                        Autonomía Real para Agentes IA
                      </h4>
                      <p>
                        MCP es el protocolo que permite a los agentes de IA no
                        solo responder preguntas, sino ejecutar tareas complejas
                        de forma autónoma, como realizar una reserva, generar un
                        informe de ventas o abrir un ticket de soporte.
                      </p>
                    </div>
                  </section>

                  <hr className='my-12 h-px border-t-0 bg-transparent bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-25' />

                  <section id='casos-de-uso'>
                    <h2 className='text-3xl mt-8 mb-6 pb-2 border-b-2 border-blue-500'>
                      Aplicaciones Reales: ¿Qué Puedes Construir con MCP?
                    </h2>
                    <p>
                      Las posibilidades son tan amplias como las APIs y sistemas
                      que quieras conectar.
                    </p>
                    <ul>
                      <li>
                        <strong>Agentes de Soporte al Cliente:</strong> Un
                        agente que demoen tiempo real el estado de un pedido en
                        tu e-commerce y gestiona una devolución conectándose a
                        tu sistema logístico.
                      </li>
                      <li>
                        <strong>Analistas de Negocio Automatizados:</strong> Un
                        agente que se conecta a tu CRM (ej. Salesforce) y a tu
                        base de datos de producción para generar informes de
                        ventas cruzando datos de ambos sistemas.
                      </li>
                      <li>
                        <strong>Asistentes de Desarrollo:</strong> Un agente
                        capaz de interactuar con repositorios de GitHub para
                        revisar cambios, o con Notion para crear y actualizar
                        páginas de documentación de forma automática.
                      </li>
                      <li>
                        <strong>Gestores de Operaciones Internas:</strong> Un
                        agente que monitoriza el inventario en tiempo real y
                        realiza pedidos a proveedores automáticamente cuando el
                        stock baja de un umbral.
                      </li>
                    </ul>
                  </section>

                  {/* CTA Final como sección */}
                  <section id='cta-agentes-ia' className='mt-16'>
                    <div className='p-8 bg-slate-900 text-white rounded-lg shadow-inner-glow shadow-inner-glow-white text-center'>
                      <h2 className='text-3xl mt-8 mb-6 pb-2 border-b-2 border-blue-500 font-bold mb-4'>
                        ¿Estás listo para construir la próxima generación de
                        Agentes IA?
                      </h2>
                      <p className='mb-6 text-slate-300 max-w-2xl mx-auto'>
                        En nuestra agencia, no solo entendemos la teoría.
                        Utilizamos protocolos de vanguardia como MCP para
                        diseñar y construir Agentes IA Automatizados que se
                        integran de forma nativa en tus flujos de trabajo,
                        resuelven problemas reales y generan un impacto medible.
                      </p>
                      <a
                        href='/demo-gratuita'
                        className='inline-block bg-blue-700
                        text-white 
                        shadow-inner-glow shadow-inner-glow-white-md 
                        hover:bg-white hover:text-blue-700 
                        hover:text-shadow-glow-blue-700 hover:bg-white 
                        drop-shadow-glow-white-xl font-bold py-3 px-6 rounded-full transition-all duration-300 text-lg'
                      >
                        Solicita tu Demo Gratuita
                      </a>
                    </div>
                  </section>
                </article>
              </div>

              {/* Menú/Índice Rápido (Sticky) */}
              <aside className='hidden lg:block w-full lg:w-1/4'>
                <div className='sticky top-28'>
                  <TableOfContents sections={sections} />
                  <div
                    className='mt-8 p-4 bg-slate-900 text-white rounded-lg 
                    shadow-inner-glow shadow-inner-glow-white-md
                    text-center'
                  >
                    <h3 className='text-2xl mt-6 mb-4 text-xl font-bold mb-3'>
                      ¿Listo para empezar?
                    </h3>
                    <p className='mb-4 text-slate-300 text-sm'>
                      Transforma tu atención al cliente con IA.
                    </p>
                    <Link
                      to='/contacto'
                      className='inline-block bg-blue-700 text-white
                      hover:bg-white hover:text-blue-700 hover:text-shadow-glow-blue-700 hover:bg-white
                      drop-shadow-glow-white-lg 
                      shadow-inner-glow shadow-inner-glow-white-sm 
                      font-bold py-2 px-4 rounded-full transition-all duration-300 hover:shadow-xl text-sm'
                    >
                      Solicita una Consulta
                    </Link>
                  </div>
                </div>
              </aside>
            </div>
          </div>
        </main>
      </div>
    </>
  );
};

export default ProtocoloMcpIaPage;
