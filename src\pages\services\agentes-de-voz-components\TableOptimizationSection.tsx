import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChair,
  faUserFriends,
  faTimesCircle,
  faCheckCircle,
  IconDefinition,
} from '@fortawesome/free-solid-svg-icons';

interface TableProps {
  capacity: number;
  status: 'wasted' | 'available' | 'optimized';
  isOccupied: boolean;
}

interface TableData {
  id: number;
  capacity: number;
  status: 'wasted' | 'available' | 'optimized';
  isOccupied: boolean;
}

interface OptimizationResult {
  tables: TableData[];
  wastedSeats: number;
  occupiedCount: number;
  description: string;
  statusText: string;
  statusIcon: IconDefinition;
  statusColor: string;
}

// Componente de Mesa actualizado para mayor contraste y legibilidad
const Table: React.FC<TableProps> = ({ capacity, status, isOccupied }) => {
  const bgColor = isOccupied
    ? status === 'wasted'
      ? 'bg-red-200/50'
      : 'bg-green-200/50'
    : 'bg-gray-200/50';
  const borderColor = isOccupied
    ? status === 'wasted'
      ? 'border-red-400'
      : 'border-green-400'
    : 'border-gray-300';
  const textColor = isOccupied
    ? status === 'wasted'
      ? 'text-red-800'
      : 'text-green-800'
    : 'text-gray-500';

  return (
    <div
      className={`relative w-full h-20 sm:h-24 rounded-lg flex flex-col items-center justify-center 
                     border-2 ${borderColor} ${bgColor} backdrop-blur-sm 
                     transition-all duration-300 transform hover:scale-105`}
    >
      <FontAwesomeIcon
        icon={faChair}
        className={`text-2xl sm:text-3xl ${textColor}`}
      />
      <span className={`mt-2 font-bold text-base sm:text-lg ${textColor}`}>
        {capacity}p
      </span>
      {isOccupied && (
        <div className='absolute -top-2 -right-2 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow'>
          <FontAwesomeIcon
            icon={status === 'wasted' ? faTimesCircle : faCheckCircle}
            className={status === 'wasted' ? 'text-red-500' : 'text-green-500'}
          />
        </div>
      )}
    </div>
  );
};

const TableOptimizationSection: React.FC = () => {
  const [numPeople, setNumPeople] = useState<number>(6);

  const handleIncrement = () => setNumPeople(prev => prev + 1);
  const handleDecrement = () => setNumPeople(prev => Math.max(1, prev - 1));

  const availableTables: TableData[] = [
    { id: 1, capacity: 4, status: 'available', isOccupied: false },
    { id: 2, capacity: 4, status: 'available', isOccupied: false },
    { id: 3, capacity: 4, status: 'available', isOccupied: false },
    { id: 4, capacity: 2, status: 'available', isOccupied: false },
    { id: 5, capacity: 2, status: 'available', isOccupied: false },
  ];

  const calculateAITables = (people: number): OptimizationResult => {
    let bestCombination: {
      occupiedTables: TableData[];
      seatedCapacity: number;
    } | null = null;

    const findOptimalSeating = (
      index: number,
      currentSeated: number,
      currentOccupiedTables: TableData[]
    ) => {
      if (currentSeated >= people) {
        const wastedSeats = currentSeated - people;
        if (
          !bestCombination ||
          currentOccupiedTables.length <
            bestCombination.occupiedTables.length ||
          (currentOccupiedTables.length ===
            bestCombination.occupiedTables.length &&
            wastedSeats < bestCombination.seatedCapacity - people)
        ) {
          bestCombination = {
            occupiedTables: [...currentOccupiedTables],
            seatedCapacity: currentSeated,
          };
        }
        return;
      }
      if (index === availableTables.length) return;
      findOptimalSeating(index + 1, currentSeated, currentOccupiedTables);
      const tableToUse = availableTables[index];
      findOptimalSeating(index + 1, currentSeated + tableToUse.capacity, [
        ...currentOccupiedTables,
        tableToUse,
      ]);
    };

    findOptimalSeating(0, 0, []);

    const finalTables: TableData[] = JSON.parse(
      JSON.stringify(availableTables)
    );
    let finalWastedSeats = 0;
    let finalDescription = `Reserva para ${people}: No se encontró una combinación óptima.`;
    let finalStatusText = 'No se encontró solución';
    let finalStatusIcon: IconDefinition = faTimesCircle;
    let finalStatusColor = 'text-red-600';

    if (bestCombination) {
      finalWastedSeats = bestCombination.seatedCapacity - people;
      bestCombination.occupiedTables.forEach(occupiedTable => {
        const tableIndex = finalTables.findIndex(
          t => t.id === occupiedTable.id
        );
        if (tableIndex !== -1) {
          finalTables[tableIndex].isOccupied = true;
          finalTables[tableIndex].status = 'optimized';
        }
      });
      const occupiedCapacities = bestCombination.occupiedTables.map(
        t => `${t.capacity}p`
      );
      finalDescription = `Reserva para ${people}: se ${occupiedCapacities.length > 1 ? 'combinan' : 'ocupa'} ${occupiedCapacities.join(' y ')}.`;
      finalStatusText =
        finalWastedSeats === 0
          ? '¡Máxima Capacidad!'
          : `${finalWastedSeats} Asientos Desperdiciados`;
      finalStatusIcon = finalWastedSeats === 0 ? faCheckCircle : faTimesCircle;
      finalStatusColor =
        finalWastedSeats === 0 ? 'text-green-700' : 'text-red-600';
    }

    return {
      tables: finalTables.sort((a, b) => a.id - b.id),
      wastedSeats: finalWastedSeats,
      occupiedCount: bestCombination
        ? bestCombination.occupiedTables.length
        : 0,
      description: finalDescription,
      statusText: finalStatusText,
      statusIcon: finalStatusIcon,
      statusColor: finalStatusColor,
    };
  };

  const calculateTraditionalTables = (people: number): OptimizationResult => {
    const tables: TableData[] = JSON.parse(JSON.stringify(availableTables));
    const occupiedTables: TableData[] = [];
    let seatedCapacity = 0;
    let description = `Reserva para ${people}: `;
    let statusText = '';
    let statusIcon: IconDefinition = faTimesCircle;
    let statusColor = 'text-red-600';

    tables.sort((a, b) => b.capacity - a.capacity);

    let chosenTable: TableData | null = null;
    for (const table of tables) {
      if (table.capacity >= people) {
        chosenTable = table;
        break;
      }
    }

    if (chosenTable) {
      const tableIndex = tables.findIndex(t => t.id === chosenTable!.id);
      tables[tableIndex].isOccupied = true;
      tables[tableIndex].status = 'wasted';
      occupiedTables.push(tables[tableIndex]);
      seatedCapacity = tables[tableIndex].capacity;
      const wastedSeats = seatedCapacity - people;
      description += `se ocupa 1 mesa/sala de ${chosenTable.capacity}.`;
      statusText = `${wastedSeats} Asientos Desperdiciados`;
      statusIcon = faUserFriends;
      statusColor = 'text-red-600';
    } else {
      description = `Reserva para ${people}: El sistema no permite reservas más de 1 mesa/sala. Haga otra reserva`;
      statusText = 'No se pudo realizar la reserva';
      statusIcon = faTimesCircle;
      statusColor = 'text-red-600';
    }

    return {
      tables: tables.sort((a, b) => a.id - b.id),
      wastedSeats: Math.max(0, seatedCapacity - people),
      occupiedCount: occupiedTables.length,
      description: description,
      statusText: statusText,
      statusIcon: statusIcon,
      statusColor: statusColor,
    };
  };

  const aiOptimization = calculateAITables(numPeople);
  const defaultOptimization = calculateTraditionalTables(numPeople);

  const inputSection = (
    <div className='my-4 lg:my-0'>
      <label
        htmlFor='numPeopleInput'
        className='block text-xl font-bold text-gray-900 mb-4'
      >
        Nº de Personas
      </label>
      <div className='flex items-center justify-center gap-2'>
        <button
          onClick={handleDecrement}
          className='w-10 h-10 bg-gray-200 text-gray-700 font-bold rounded-full text-xl hover:bg-gray-300 transition-colors'
          aria-label='Reducir número de personas'
        >
          -
        </button>
        <input
          type='number'
          id='numPeopleInput'
          value={numPeople}
          onChange={e =>
            setNumPeople(Math.max(1, parseInt(e.target.value) || 0))
          }
          min='1'
          className='p-3 border-2 border-gray-300 rounded-lg text-center text-2xl font-bold w-24'
        />
        <button
          onClick={handleIncrement}
          className='w-10 h-10 bg-gray-200 text-gray-700 font-bold rounded-full text-xl hover:bg-gray-300 transition-colors'
          aria-label='Aumentar número de personas'
        >
          +
        </button>
      </div>
    </div>
  );

  return (
    <section className='relative py-20 overflow-hidden bg-gradient-to-r from-red-200 from-0% via-white to-green-200 shadow-inner-glow shadow-inner-glow-white-2xl min-h-screen flex items-center justify-center'>
      <div className='absolute inset-0 flex items-center justify-center z-0 bg-gradient-to-b from-white from-10% via-transparent via-65% to-white'></div>

      <div className='relative max-w-7xl mx-auto text-center px-4 z-10'>
        <h2 className='text-4xl sm:text-5xl font-extrabold text-gray-900 mb-4 tracking-tight text-shadow-glow-white-sm'>
          Optimización Inteligente de Mesas y Salas
        </h2>
        <p className='text-base sm:text-lg text-gray-800 mb-12 max-w-3xl mx-auto'>
          No solo toma reservas, las optimiza. Evita asientos vacíos y maximiza
          la capacidad de tu restaurante, clínica o cualquier otro negocio.
        </p>

        <div className='grid grid-cols-1 lg:grid-cols-[2fr,1fr,2fr] gap-4 lg:gap-8 items-center'>
          {/* Card 1: Sistema Estandarizado */}
          <div className='lg:col-span-1'>
            <div
              className='shadow-inner-glow shadow-inner-glow-red-200-2xl bg-white backdrop-blur-lg p-6 sm:p-8 rounded-2xl border border-red-200 
                        drop-shadow-glow-red-200-2xl transform transition-all duration-500 hover:-translate-y-2 
                        hover:drop-shadow-glow-red-600-2xl h-full'
            >
              <h4 className='text-2xl sm:text-3xl font-bold mb-4 text-red-700'>
                Sistema Estandarizado
              </h4>
              <p className='text-center text-gray-700 mb-4 text-sm sm:text-base'>
                {defaultOptimization.description}
              </p>
              <div className='bg-red-50 p-4 sm:p-6 rounded-xl'>
                <div className='grid grid-cols-3 gap-2 sm:gap-4'>
                  {defaultOptimization.tables.map(table => (
                    <Table key={table.id} {...table} />
                  ))}
                </div>
                <div
                  className={`mt-4 text-center font-semibold text-lg sm:text-xl flex items-center justify-center ${defaultOptimization.statusColor}`}
                >
                  <FontAwesomeIcon
                    icon={defaultOptimization.statusIcon}
                    className='mr-3'
                  />
                  <span>{defaultOptimization.statusText}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Input Section */}
          <div className='lg:col-span-1'>{inputSection}</div>

          {/* Card 2: Nuestro Asistente IA */}
          <div className='lg:col-span-1'>
            <div
              className='shadow-inner-glow shadow-inner-glow-green-200-2xl bg-white backdrop-blur-lg p-6 sm:p-8 rounded-2xl border border-green-200 
                         transform transition-all duration-500 hover:-translate-y-2 hover:drop-shadow-glow-green-600-2xl h-full'
            >
              <h4 className='text-2xl sm:text-3xl font-bold mb-4 text-green-700'>
                Nuestro Asistente IA
              </h4>
              <p className='text-center text-gray-700 mb-4 text-sm sm:text-base'>
                {aiOptimization.description}
              </p>
              <div className='bg-green-100/50 p-4 sm:p-6 rounded-xl border border-green-200'>
                <div className='grid grid-cols-3 gap-2 sm:gap-4'>
                  {aiOptimization.tables.map(table => (
                    <Table key={table.id} {...table} />
                  ))}
                </div>
                <div
                  className={`mt-4 text-center font-semibold text-lg sm:text-xl flex items-center justify-center ${aiOptimization.statusColor}`}
                >
                  <FontAwesomeIcon
                    icon={aiOptimization.statusIcon}
                    className='mr-3'
                  />
                  <span>{aiOptimization.statusText}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TableOptimizationSection;
