import React from 'react';
import { Link } from 'react-router-dom';

const HeroSectionIntroduction: React.FC = () => {
  return (
    <div
      className='relative overflow-x-hidden
    bg-gradient-to-b from-blue-900 via-white via-20% to-white
    shadow-inner-glow shadow-inner-glow-white-right-2xl shadow-inner-glow-white-left-2xl
    mb-16 pt-20 pb-10 sm:pt-24 sm:pb-12 lg:pt-32 lg:pb-16'
      /*
      NOTA DE SEGURIDAD Y LAYOUT:
      'overflow-x-hidden' se añade aquí para contener el desbordamiento horizontal
      causado por la combinación de 'gap-x-8' y el ancho de los elementos internos
      en pantallas más pequeñas, evitando que afecte el scroll global de la página.
    */
    >
      <div className='container mx-auto px-4 mt-10'>
        <div className='max-w-4xl mx-auto text-center'>
          <h1 className='font-bold tracking-tight text-gray-900 text-5xl lg:text-6xl'>
            La IA que trabaja por ti
          </h1>
          <div className='mt-6 flex flex-col sm:flex-row items-center justify-between gap-x-8'>
            <p className='sm:w-2/3 text-md leading-8 text-gray-800 text-center sm:text-left'>
              Automatizamos tus chats y llamadas para que ofrezcas una
              experiencia de cliente excepcional, 24/7. Nos integramos con tus
              sistemas para que no pierdas ni una oportunidad de negocio.
            </p>
            <div className='mt-6 sm:mt-0 flex-shrink-0 flex flex-col gap-y-4'>
              <Link
                to='/empezar'
                className='text-white bg-blue-800 px-5 py-3 rounded-full text-base font-medium transition-all duration-200
                hover:text-shadow-glow-blue-800 hover:text-white 
                hover:bg-blue-800 hover:border-white hover:text-white
                hover:shadow-inner-glow hover:shadow-inner-glow-white-2xl
                shadow-inner-glow shadow-inner-glow-white-sm
                text-shadow-glow-white'
              >
                Empieza Gratis
              </Link>
              <Link
                to='/demos'
                className='text-blue-800 px-5 py-3 rounded-full text-base font-medium transition-all duration-200
                border border-[1px] border-blue-800
                hover:text-shadow-glow-white hover:text-white hover:bg-blue-800 hover:border-white
                hover:text-shhadow-glow-blue-800 hover:text-white
                hover:shadow-inner-glow hover:shadow-inner-glow-white-2xl
                text-shadow-glow-white drop-shadow-glow-white text-center'
              >
                Ver una Demo <span aria-hidden='true'>→</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSectionIntroduction;
