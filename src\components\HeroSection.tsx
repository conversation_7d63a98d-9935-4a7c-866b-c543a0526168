// RUTA: src/components/HeroSection.tsx

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { gsap } from 'gsap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faGraduationCap } from '@fortawesome/free-solid-svg-icons';
import flechaResplandor from '@/assets/svg/flecha_garabato_blanca_apuntando_hacia_arriba_curva.svg';

// Asegúrate de que esta ruta de importación sea correcta para tu proyecto.
// Si tu alias '@' no está configurado, usa rutas relativas como './FondoAnimadoBurbujas'

import BotonHeroSectionCTA from '@/components/BotonHeroSectionCTA.tsx';
import InstagramChatMockup from '@/components/InstagramChatMockup.tsx';
import PhoneCallMockup from '@/components/PhoneCallMockup.tsx';

export default function HeroSection() {
  const heroRef = useRef<HTMLDivElement>(null);
  const badgeRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const paragraphRef = useRef<HTMLParagraphElement>(null);

  const buttonsRef = useRef<HTMLDivElement>(null);
  const inteligenciaRef = useRef<HTMLSpanElement>(null);
  const artificialRef = useRef<HTMLSpanElement>(null);
  const mockupContainerRef = useRef<HTMLDivElement>(null); // Nueva referencia
  // Nuevo estado para controlar el inicio de la animación de Instagram
  const [startInstagramAnimation, setStartInstagramAnimation] = useState(false);
  const startPhoneCallAnimation = true; // Inicia inmediatamente, no necesita ser un estado si no cambia
  // Nuevo estado para mostrar el resumen del chat del teléfono
  const [showPhoneCallSummary, setShowPhoneCallSummary] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const ctx = gsap.context(() => {
      const tl = gsap.timeline({ delay: 0.2 }); // Faster start

      // Existing text animations
      tl.fromTo(
        badgeRef.current,
        { y: -50, opacity: 0, scale: 0.8 },
        { y: 0, opacity: 1, scale: 1, duration: 0.3, ease: 'back.out(1.7)' } // Faster
      );
      tl.fromTo(
        titleRef.current,
        { y: 50, opacity: 0, scale: 0.9 },
        { y: 0, opacity: 1, scale: 1, duration: 0.4, ease: 'power3.out' }, // Faster
        '-=0.2'
      );
      tl.fromTo(
        inteligenciaRef.current,
        { y: 30, opacity: 0, scale: 0.8, rotationX: 45 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          rotationX: 0,
          duration: 0.3, // Faster
          ease: 'back.out(1.7)',
        },
        '-=0.1'
      );
      tl.fromTo(
        artificialRef.current,
        { y: 30, opacity: 0, scale: 0.8, rotationX: 45 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          rotationX: 0,
          duration: 0.3, // Faster
          ease: 'back.out(1.7)',
        },
        '+=0.1'
      );
      tl.fromTo(
        paragraphRef.current,
        { y: 30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.3, ease: 'power2.out' }, // Faster
        '-=0.1'
      );
      tl.fromTo(
        buttonsRef.current,
        { y: 40, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.3, ease: 'power2.out' }, // Faster
        '-=0.2'
      );

      tl.to(
        inteligenciaRef.current,
        {
          duration: 0.2, // Faster
          ease: 'power2.out',
          onComplete: () => {
            inteligenciaRef.current?.classList.add(
              'animate-underline-blue-gradient-white-base'
            );
          },
        },
        '+=0.2'
      );
      tl.to(
        artificialRef.current,
        {
          duration: 0.2, // Faster
          ease: 'power2.out',
          onComplete: () => {
            artificialRef.current?.classList.add(
              'animate-underline-blue-gradient-white-base'
            );
          },
        },
        '+=0.2'
      );

      // After text animations, start Instagram mockup animation
      tl.to(
        mockupContainerRef.current,
        {
          opacity: 1,
          scale: 1,
          y: 0,
          duration: 0.5, // Faster
          ease: 'back.out(1.7)',
          onStart: () => setStartInstagramAnimation(true), // Trigger Instagram animation
        },
        '>-0.5' // Inicia 0.5 segundos antes del final de la animación anterior
      );
    }, heroRef);

    return () => ctx.revert();
  }, []);

  const handleInstagramAnimationComplete = useCallback(() => {
    setShowPhoneCallSummary(true); // Show phone call summary after Instagram animation
  }, []);

  return (
    /*
      NOTA DE SEGURIDAD Y LAYOUT:
      'overflow-x-hidden' se añade aquí para contener el desbordamiento horizontal
      causado por los elementos rotados (mockups de Instagram y Phone Call)
      dentro de esta sección, evitando que afecte el scroll global de la página.
    */
    <section
      ref={heroRef}
      className='relative xl-custom:min-h-[100vh] xl-mockup:min-h-[130vh] md:min-h-[10vh] sm:min-h-[50vh] overflow-x-hidden flex items-start justify-center pt-5 pb-20
      bg-gradient-to-b 
      from-white from-40% 
      via-blue-750 via-70% 
      to-blue-900
      shadow-inner-glow shadow-inner-glow-white-right-2xl shadow-inner-glow-white-left-2xl'
    >
      {/* <FondoAnimadoBurbujas /> */}

      <div className='relative z-40 max-w-7xl mx-auto px-6 sm:px-6 lg:px-8'>
        <div className='text-center'>
          <div
            ref={badgeRef}
            className='inline-flex items-center space-x-2 bg-blue-600/10 backdrop-blur-sm px-4 py-2 rounded-full mb-8 border border-blue-600/20'
          >
            <FontAwesomeIcon
              icon={faGraduationCap}
              className='h-4 w-4 text-red-600'
            />
            <span className='text-sm font-medium text-blue-700'>
              Expertos titulados en IA
            </span>
          </div>
          <h1
            className='text-5xl md:text-7xl font-extrabold tracking-tight leading-tight
            text-gray-900 drop-shadow-glow-white-md'
          >
            Automatiza tu{' '}
            <span
              ref={artificialRef}
              className='text-radial-gradient-from-blue-600-to-blue-700 '
            >
              atención al cliente{' '}
            </span>
            <span className='text-5xl md:text-7xl font-extrabold inline-block sm:pb-2 leading-tight'>
              y haz crecer tu{' '}
              <span
                ref={artificialRef}
                className='text-radial-gradient-from-blue-600-to-blue-700 '
              >
                negocio
              </span>
            </span>
          </h1>
          <p
            ref={paragraphRef}
            className='text-base text-center font-medium text-md mb-12 mt-4 max-w-2xl mx-auto leading-relaxed px-4 sm:px-0
             text-white text-shadow-glow-black-sm drop-shadow-glow-black-md
             md:text-black md:text-shadow-glow-white-sm md:drop-shadow-glow-white-md '
          >
            Tus propios <strong>Agentes IA </strong> responden por ti{' '}
            <strong> llamadas y chats 24/7</strong> <br />
            Mejoran la satisfacción de tus clientes y se integran con las
            herramientas que ya usas
            <span className='md:static-gradient-underline-blue static-gradient-underline-white'>
              ahorrando horas de trabajo{' '}
            </span>
          </p>
          <div
            ref={buttonsRef}
            className='flex flex-col sm:flex-row gap-6 sm:gap-8 justify-center items-center px-4 sm:px-0 w-fit mx-auto xl-custom:mt-24'
          >
            <div
              className='group cursor-pointer pointer-events-auto'
              onClick={() => navigate('/demo-gratuita')}
            >
              <BotonHeroSectionCTA />
              <p className='mt-[-3.5rem] ml-[-2.5rem] text-sm text-white drop-shadow-glow-black-xs font-small flex justify-center items-center gap-3 group-hover:text-green-300 group-hover:drop-shadow-glow-green-800-md transition-all duration-300'>
                <img
                  src={flechaResplandor}
                  alt='Flecha decorativa apuntando al botón'
                  className='w-16 h-16 mt-[1.5rem] group-hover:filter group-hover:brightness-0 group-hover:invert drop-shadow-glow-black' // Ajusta el tamaño como necesites
                />
                <span className='mt-[5.5rem] font-small position-relative ml-[-2.5rem] drop-shadow-glow-black-xxs'>
                  ¡Pon a prueba a <strong>tu futura IA</strong>!
                  <br />
                  <strong>15 minutos</strong> que cambiarán tu forma de trabajar
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
      {/* Mockups Container */}
      <div className='absolute inset-0 flex items-center justify-center z-40 pointer-events-none hidden xl-mockup:flex'>
        {/* Instagram Chat Mockup */}
        <div
          ref={mockupContainerRef} // Añadida la referencia
          className='absolute top-[50vh] left-[5vw] transform rotate-[-6deg] z-0 w-[24rem] drop-shadow-glow-white hidden md:block lg:top-[45vh] xl-custom:top-[30vh]' // Ocultar en móvil y mover a la derecha
        >
          <Link to='/servicios/agentes-de-chat-ia'>
            <InstagramChatMockup
              startMessagesAnimation={startInstagramAnimation}
              onAnimationComplete={handleInstagramAnimationComplete}
              isMobile={false}
            />
          </Link>
        </div>

        {/* Phone Call Mockup */}
        <div
          className='absolute top-[50vh] right-[5vw] transform rotate-[6deg] z-0 w-[24rem] drop-shadow-glow-white hidden md:block lg:top-[45vh] xl-custom:top-[30vh]' // Ocultar en móvil y mover a la izquierda
        >
          <Link to='/servicios/agentes-de-voz-ia'>
            <PhoneCallMockup
              startMessagesAnimation={startPhoneCallAnimation}
              showChatContent={showPhoneCallSummary}
              isMobile={false}
            />
          </Link>
        </div>
      </div>
    </section>
  );
}
