import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheckCircle,
  faTimesCircle,
  faBrain,
  faLink,
  faLightbulb,
  faComments,
  faBolt,
  faFileAlt,
  faChartLine,
  faImage,
} from '@fortawesome/free-solid-svg-icons';
import HuggingFaceIcon from '../../../components/HuggingFaceIcon';

const CompetitorComparisonSection = () => {
  const comparisonData = [
    {
      feature: 'Soporte Multimedia',
      featureIcon: faImage,
      competitors: 'Solo texto. No pueden enviar imágenes, videos o enlaces interactivos. 🚫',
      ourAgent: 'Soporte completo para imágenes, videos, audios y enlaces interactivos. 🖼️📹🔗',
    },
    {
      feature: 'Personalización y Contexto',
      featureIcon: faBrain,
      competitors:
        'Respuestas genéricas y repetitivas. Olvidan el historial de conversación al instante. 😵',
      ourAgent:
        'Recuerda cada interacción. Ofrece respuestas 100% personalizadas y contextuales que deleitan a tus clientes. ✨',
    },
    {
      feature: 'Personalización',
      featureIcon: faFileAlt,
      competitors:
        'Respuestas genéricas y falta de adaptación al historial del cliente. 🤖',
      ourAgent:
        'Agentes que aprenden de las interacciones para ofrecer respuestas más relevantes y personalizadas. 🧠',
    },
    {
      feature: 'Conversaciones Humanas',
      featureIcon: faComments,
      competitors:
        'Robóticos y frustrantes. Bucles de respuestas y "no he entendido" que acaban en un cliente molesto. 🤖',
      ourAgent:
        'Tono y flujo natural. Capaz de entender sarcasmo, modismos y el contexto real de la conversación. 😎',
    },
    {
      feature: 'Inteligencia y Proactividad',
      featureIcon: faLightbulb,
      competitors:
        'Reactivos y pasivos. Solo responden a preguntas directas y simples. No toman la iniciativa. 😴',
      ourAgent:
        'Agente proactivo. Inicia conversaciones, anticipa necesidades y guía a los usuarios hacia la conversión. 🚀',
    },
    {
      feature: 'Integración Multicanal',
      featureIcon: faLink,
      competitors:
        'Sistemas aislados. Dificultad para unificar la comunicación entre WhatsApp, web e Instagram. 🤯',
      ourAgent:
        'Plataforma unificada. Gestiona todas las conversaciones desde un solo lugar, sin fisuras ni complicaciones.',
    },
    {
      feature: 'Rapidez',
      featureIcon: faBolt,
      competitors:
        'Tiempos de espera prolongados para obtener una respuesta. ⏳',
      ourAgent:
        'Resolución de dudas y provisión de información al instante, mejorando la experiencia del usuario. ⚡',
    },
    {
      feature: 'Disponibilidad 24/7',
      featureIcon: faCheckCircle,
      competitors:
        'Horarios limitados o respuestas lentas fuera del horario comercial. 😴',
      ourAgent:
        'Atención inmediata 24/7, 365 días al año, sin importar la hora o el día. ⏰',
    },
    {
      feature: 'Escalabilidad',
      featureIcon: faChartLine,
      competitors:
        'Planes de precios confusos que penalizan el crecimiento. Costes ocultos por "funciones avanzadas". 👎 Dificultad para manejar grandes volúmenes de consultas sin comprometer la calidad. 🚧',
      ourAgent:
        'Escalabilidad transparente y sin sorpresas. Un plan claro que crece contigo, optimizando tu ROI. 📈 Manejo de un volumen ilimitado de consultas sin comprometer la calidad del servicio. 🚀',
    },
  ];

  return (
    <section
      className='py-16 sm:py-24 
      bg-[radial-gradient(circle_at_center,_white_0%,_white_95%,_rgba(236,72,153,0.3)_97%,_rgba(168,85,247,0.5)99%)] shadow-inner-glow shadow-inner-glow-white-2xl
      md:bg-[radial-gradient(circle_at_center,_white_0%,_white_65%,_rgba(236,72,153,0.3)_75%,_rgba(168,85,247,0.5)85%)]
      shadow-inner-glow shadow-inner-glow-white-2xl'
    >
      <div className='md:max-w-7xl mx-auto md:px-4 sm:px-6 lg:px-8'>
        <div className='text-center'>
          <h2 className='text-base font-bold text-purple-600 tracking-wide uppercase'>
            La Diferencia Real
          </h2>
          <p className='mt-2 text-3xl font-extrabold text-gray-900 sm:text-4xl'>
            No todos los Chatbots son Iguales
          </p>
          <p className='mt-4 max-w-2xl mx-auto textl font-semibold text-gray-700'>
            Los chatbots básicos son cosa del pasado. Mientras la competencia se
            enfoca en respuestas automáticas, nosotros creamos cerebros
            digitales que potencian tu negocio.
          </p>
        </div>

        <div className='mt-12'>
          <div className='bg-white drop-shadow-glow-pink-500-bottom-md rounded-xl relative'>
            <div className='divide-y divide-purple-200'>
              <div className='text-xl grid sm:grid-cols-3 grid-cols-1 text-center text-white sticky top-[64px] z-10 rounded'>
                <div
                  className='text-lg px-2 py-2 xs:px-0 sm:px-6 sm:py-4 text-shadow-glow-blue-900-xl bg-gradient-to-t from-blue-500 from-10% to-blue-700 font-bold 
                 shadow-inner-glow shadow-inner-glow-white-left-xl shadow-inner-glow-white-top-xl shadow-inner-glow-white-bottom-xl md:rounded-tl-xl'
                >
                  Características
                </div>
                <div
                  className='text-lg px-2 py-2 sm:px-6 sm:py-4 text-shadow-glow-black-100-sm md:text-shadow-glow-black-100-sm
                 bg-gradient-to-t from-red-300 from-10% to-red-500
                 md:bg-gradient-to-t md:from-blue-500 md:from-10% md:to-blue-700
                 font-bold  
                 shadow-inner-glow shadow-inner-glow-white-right-xl  shadow-inner-glow-white-top-xl shadow-inner-glow-white-bottom-xl'
                >
                  🤖 Otros chatbots
                </div>
                <div
                  className='text-lg px-2 py-2 sm:px-6 sm:py-4 text-shadow-glow-black-sm bg-gradient-to-t from-green-500 from-10% to-green-800 font-bold 
                 shadow-inner-glow shadow-inner-glow-white-xl drop-shadow-glow-forest-green-400-sm md:rounded-tr-xl flex items-center justify-center'
                >
                  <HuggingFaceIcon className='mr-2' height='2em' width='2em' />{' '}
                  Nuestro Agente IA
                </div>
              </div>
              <div className='divide-y divide-purple-200'>
                {comparisonData.map((item, index) => (
                  <div
                    key={index}
                    className='grid sm:grid-cols-3 grid-cols-1 gap-x-4 py-5 px-6 md:py-2 md:px-3 items-center shadow-inner-glow shadow-inner-glow-blue-100-lg last:rounded-b-xl'
                  >
                    <div className='font-semibold text-gray-900 flex items-center text-md pb-4 px-4 sm:col-span-1'>
                      <FontAwesomeIcon
                        icon={item.featureIcon}
                        className='h-8 w-8 mr-4 text-blue-500'
                      />
                      {item.feature}
                    </div>
                    <div className='flex items-start bg-red-200 md:bg-red-100/50 p-4 rounded-lg  shadow-inner-glow shadow-inner-glow-white-xl sm:col-span-1'>
                      <FontAwesomeIcon
                        icon={faTimesCircle}
                        className='h-4 w-4 text-red-500 mr-3'
                      />
                      <span className='ml-3 text-gray-800'>
                        {item.competitors}
                      </span>
                    </div>
                    <div className='flex items-start bg-green-50 p-4 rounded-lg shadow-inner-glow shadow-inner-glow-green-200-xl sm:col-span-1'>
                      <FontAwesomeIcon
                        icon={faCheckCircle}
                        className='h-5 w-5 text-green-500 mr-3'
                      />
                      <span className='ml-3 text-gray-800 font-medium'>
                        {item.ourAgent}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className='mt-10 text-center max-w-2xl mx-auto px-4'>
          <p className='text-lg text-gray-800 mb'>
            La elección es clara. No te conformes con un contestador automático
            glorificado.
            <br />
            <span className='pt-4 mt-4 block font-bold text-purple-700'>
              Invierte en una solución de IA que realmente entienda a tus
              clientes y haga crecer tu negocio.
            </span>
          </p>
        </div>
      </div>
    </section>
  );
};

export default CompetitorComparisonSection;
