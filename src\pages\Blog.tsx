import React from 'react';
import { blogPosts } from '@/constants/blog';
import BlogPostCard from '@/components/BlogPostCard';

const BlogPage = () => {
  return (
    <div className='bg-white'>
      <header className='relative mt-2 pb-6 bg-gradient-to-b from-white via-blue-600 via-70% to-blue-800 iphone-xr-pro-max:py-2 shadow-inner-glow shadow-inner-glow-white-left-2xl shadow-inner-glow-white-right-2xl'>
        <div className='max-w-7xl mx-auto px-6 sm:px-6 lg:px-8 text-center mt-6'>
          <h1 className='text-6xl font-bold tracking-xl font-extrabold tracking-tight text-white drop-shadow-glow-blue-800-md text-shadow-glow-blue-900-sm'>
            Nuestro Blog
          </h1>
          <p className='mt-2 text-lg md:text-xl mx-auto text-white drop-shadow-glow-blue-800-md text-shadow-glow-blue-900-sm'>
            Explora nuestros artículos sobre inteligencia artificial, desarrollo
            de software y más.
          </p>
        </div>
      </header>
      <main
        className='iphone-xr-pro-max:py-2
      bg-gradient-to-b from-blue-800 from-[150px] via-white via-[300px] to-white shadow-inner-glow shadow-inner-glow-white-left-2xl shadow-inner-glow-white-right-2xl'
      >
        <div className='max-w-7xl mx-auto py-6 px-6 sm:px-6 lg:px-6 bg-white rounded-t-xl drop-shadow-glow-white-md'>
          <div className='grid gap-16 lg:grid-cols-2'>
            {blogPosts.map(post => (
              <BlogPostCard key={post.slug} post={post} />
            ))}
          </div>
        </div>
      </main>
    </div>
  );
};

export default BlogPage;
