// src/utils/gcs.ts

const VITE_GCS_BASE_IMAGES_URL = import.meta.env.VITE_GCS_BASE_MEDIA_IMAGES_URL;
const VITE_GCS_BASE_VIDEOS_URL = import.meta.env.VITE_GCS_BASE_VIDEOS_URL;
const VITE_GCS_BASE_SOUNDS_URL = import.meta.env.VITE_GCS_BASE_SOUNDS_URL;

if (!VITE_GCS_BASE_IMAGES_URL) {
  console.error(
    'Error: VITE_GCS_BASE_IMAGES_URL is not defined in your .env file.'
  );
}

if (!VITE_GCS_BASE_SOUNDS_URL) {
  console.error(
    'Error: VITE_GCS_BASE_SOUNDS_URL is not defined in your .env file.'
  );
}

/**
 * Constructs the full public URL for a Google Cloud Storage object for Images.
 * @param objectName - The name/path of the object in the bucket (e.g., 'main-logo.png').
 * @returns The full public URL.
 */
export const getCloudImageUrl = (objectName: string): string => {
  if (!objectName) return ''; // Return empty string if no name is provided
  return `${VITE_GCS_BASE_IMAGES_URL}/${objectName}`;
};

/**
 * Constructs the full public URL for a Google Cloud Storage object for Videos
 * @param objectName - The name/path of the object in the bucket (e.g., 'main-logo.png').
 * @returns The full public URL.
 */
export const GetCloudVideoUrl = (objectName: string): string => {
  if (!objectName) return ''; // Return empty string if no name is provided
  return `${VITE_GCS_BASE_VIDEOS_URL}/${objectName}`;
};

/**
 * Constructs the full public URL for a Google Cloud Storage object for Sounds.
 * @param objectName - The name/path of the object in the bucket (e.g., 'audio.mp3').
 * @returns The full public URL.
 */
export const getGcsFileUrl = (objectName: string): string => {
  if (!objectName) return ''; // Return empty string if no name is provided
  return `${VITE_GCS_BASE_SOUNDS_URL}/${objectName}`;
};
