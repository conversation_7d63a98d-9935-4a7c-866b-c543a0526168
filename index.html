<!doctype html>
<html lang="es" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon_taratic.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <title>TARATIC - Consultoria en Inteligencia Artificial</title>
    <meta
      name="description"
      content="Transformamos tu negocio con soluciones de Inteligencia Artificial. Automatización, agentes IA, asistentes de voz y más."
    />
    <!-- Google tag (gtag.js) -->
    <!-- Google tag (gtag.js) -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-VPCHR7VSNQ"
    ></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag('js', new Date());

      // Default consent to 'denied' for all categories
      gtag('consent', 'default', {
        ad_storage: 'denied',
        analytics_storage: 'denied',
        functionality_storage: 'denied',
        personalization_storage: 'denied',
        security_storage: 'granted', // Security storage is usually granted by default
      });

      gtag('config', 'G-VPCHR7VSNQ');

      // Function to update consent based on user choice (e.g., from a cookie banner)
      // This function should be called by your Consent Management Platform (CMP)
      window.updateGoogleConsent = function (granted) {
        gtag('consent', 'update', {
          ad_storage: granted ? 'granted' : 'denied',
          analytics_storage: granted ? 'granted' : 'denied',
          functionality_storage: granted ? 'granted' : 'denied',
          personalization_storage: granted ? 'granted' : 'denied',
        });
      };
    </script>
    <!-- End Google tag (gtag.js) -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body class="font-inter">
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
