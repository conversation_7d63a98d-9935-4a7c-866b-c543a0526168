import React from 'react';
import ChatWindow from './ChatWindow';

interface ChatOverlayProps {
  isOpen: boolean;
  onClose: () => void;
}

const ChatOverlay: React.FC<ChatOverlayProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div
      className='fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center h-screen w-screen'
      onClick={onClose}
    >
      <div onClick={e => e.stopPropagation()} className='flex flex-col'>
        {' '}
        {/* Evita que el clic en la ventana cierre el chat */}
        <ChatWindow onClose={onClose} />
      </div>
    </div>
  );
};

export default ChatOverlay;
