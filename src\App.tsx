import React, { useState, useEffect, Suspense, lazy } from 'react';
import { Route, Routes, useLocation } from 'react-router-dom';
import { Header, Footer, FloatingElements, ConsentBanner } from '@/components';
import ScrollToTop from '@/components/ScrollToTop';
import Home from '@/pages/Home';
import Contact from '@/pages/Contact';
import ConsultaGratuita from '@/pages/ConsultaGratuita';
import AistentesDeVozIaPage from '@/pages/blog/AsistentesDeVozIa';
import BlogPage from '@/pages/Blog';
import ServiciosPage from '@/pages/Servicios';

import AgentesDeChatIAMulticanal from '@/pages/services/AgentesDeChatIAMulticanal';
import AgentesDeVoz from '@/pages/services/AgentesDeVoz';
import AutomatizacionInteligente from '@/pages/AutomatizacionInteligente';
import GestionInteligenteDeCorreo from '@/pages/GestionInteligenteDeCorreo';
import WebsConIaIntegrada from '@/pages/WebsConIaIntegrada';
import Empezar from '@/pages/Empezar';
import ActivarPrueba from '@/pages/ActivarPrueba';
import PoliticaDeCookies from '@/pages/PoliticaDeCookies';

// Importar los nuevos componentes de chat
import ChatButton from '@/components/chat/ChatButton';
const ChatOverlay = lazy(() => import('@/components/chat/ChatOverlay'));

function App() {
  const location = useLocation();
  const [isChatOpen, setChatOpen] = useState(false);

  const openChat = () => setChatOpen(true);
  const closeChat = () => setChatOpen(false);

  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        closeChat();
      }
    };

    if (isChatOpen) {
      document.body.style.overflow = 'hidden';
      window.addEventListener('keydown', handleEscKey);
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      window.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'auto';
    };
  }, [isChatOpen]);

  useEffect(() => {
    if (import.meta.env.PROD) {
      fetch(
        'https://n8n-n8n.dxksiz.easypanel.host/webhook/page-view-notification',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-taratic': 'x-taratic-canarias',
          },
          body: JSON.stringify({
            url: window.location.href,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
          }),
        }
      ).catch(error =>
        console.error('Error sending page view notification:', error)
      );
    }
  }, [location.pathname]);

  return (
    <div className='min-h-screen bg-white'>
      <ScrollToTop />
      <Header />
      <main className='pt-16 flex mx-auto flex-wrap flex-col'>
        <Routes>
          <Route path='/' element={<Home />} />
          <Route path='/empezar' element={<Empezar />} />
          <Route path='/contacto' element={<Contact />} />
          <Route path='/demo-gratuita' element={<ConsultaGratuita />} />
          <Route path='/blog' element={<BlogPage />} />
          <Route path='/servicios' element={<ServiciosPage />} />
          <Route
            path='/servicios/agentes-de-chat-ia'
            element={<AgentesDeChatIAMulticanal />}
          />
          <Route
            path='/servicios/agentes-de-voz-ia'
            element={<AgentesDeVoz />}
          />
          <Route
            path='/servicios/automatizacion-inteligente'
            element={<AutomatizacionInteligente />}
          />
          <Route
            path='/servicios/gestion-inteligente-de-correo'
            element={<GestionInteligenteDeCorreo />}
          />
          <Route
            path='/servicios/webs-con-ia-integrada'
            element={<WebsConIaIntegrada />}
          />
          <Route
            path='/blog/asistentes-de-voz-ia'
            element={<AistentesDeVozIaPage />}
          />
          <Route path='/activar-prueba' element={<ActivarPrueba />} />
          <Route path='/politica-de-cookies' element={<PoliticaDeCookies />} />
        </Routes>
      </main>
      <Footer />
      <FloatingElements />
      <ConsentBanner />

      {/* Integración del nuevo widget de chat */}
      {!isChatOpen && <ChatButton onClick={openChat} />}
      <Suspense fallback={null}>
        {isChatOpen && <ChatOverlay isOpen={isChatOpen} onClose={closeChat} />}
      </Suspense>
    </div>
  );
}

export default App;
