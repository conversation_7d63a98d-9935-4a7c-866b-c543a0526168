import React from 'react';
import { services } from '@/constants/services';
import ServiceCard from '@/components/ServiceCard';

const ServiciosPage = () => {
  return (
    <div className='bg-white'>
      <header className='relative mt-2 pb-6 bg-gradient-to-b from-white via-blue-600 via-70% to-blue-800 iphone-xr-pro-max:py-4 shadow-inner-glow shadow-inner-glow-white-left-2xl shadow-inner-glow-white-right-2xl'>
        <div className='max-w-7xl mx-auto px-6 sm:px-6 lg:px-8 text-center mt-6'>
          <h1
            className='text-6xl font-bold tracking-xl font-extrabold tracking-tight
          text-white drop-shadow-glow-blue-800-md text-shadow-glow-blue-900-sm'
          >
            Nuestros Servicios
          </h1>
          <p className='mt-2 text-lg md:text-xl mx-auto text-white drop-shadow-glow-blue-800-md text-shadow-glow-blue-900-sm'>
            Soluciones de inteligencia artificial para potenciar tu negocio.
          </p>
        </div>
      </header>
      <main
        className='iphone-xr-pro-max:py-2
      bg-gradient-to-b from-blue-800 from-[150px] via-white via-[300px] to-white 
      shadow-inner-glow shadow-inner-glow-white-left-2xl shadow-inner-glow-white-right-2xl'
      >
        <div className='max-w-7xl mx-auto py-6 px-6 sm:px-6 lg:px-6 bg-white rounded-t-xl drop-shadow-glow-white-md'>
          <div className='grid gap-16 lg:grid-cols-2'>
            {services.map(service => (
              <ServiceCard
                key={service.id}
                service={service}
                currentPage='servicios'
              />
            ))}
          </div>
        </div>
      </main>
    </div>
  );
};

export default ServiciosPage;
