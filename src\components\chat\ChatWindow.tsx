import React, { useEffect, useState } from 'react';
import Cha<PERSON>Header from './ChatHeader';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import { useChat } from '@/hooks/useChat';

interface ChatWindowProps {
  onClose: () => void;
}

const ChatWindow: React.FC<ChatWindowProps> = ({ onClose }) => {
  const { messages, isTyping, sendMessage } = useChat();
  return (
    <div
      className='w-[90vw] h-[90vh] sm:w-[40vw] sm:h-[70vh] bg-white/60 backdrop-blur-sm rounded-3xl drop-shadow-glow-white-2xl flex flex-col flex-grow relative'
    >
      <ChatHeader onClose={onClose} />
      <MessageList messages={messages} isTyping={isTyping} keyboardHeight={50} />
      <MessageInput onSendMessage={sendMessage} />
    </div>
  );
};

export default ChatWindow;
