import React from 'react';

const KeyBenefitsSection = () => {
  return (
    <section className='py-16 bg-white flex items-center justify-center'>
      <div className='container mx-auto px-4'>
        <h2 className='text-5xl font-extrabold text-gray-800 mb-12 text-center'>
          Beneficios que Impulsan tu Negocio
        </h2>
        <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-8'>
          <div className='bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-200'>
            <h3 className='text-3xl font-semibold text-purple-700 mb-4'>
              Interacción Natural
            </h3>
            <p className='text-gray-700 text-lg'>
              Tecnología de voz avanzada para conversaciones fluidas y
              comprensivas, como si hablaras con una persona real.
            </p>
          </div>
          <div className='bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-200'>
            <h3 className='text-3xl font-semibold text-purple-700 mb-4'>
              Automatización Eficiente
            </h3>
            <p className='text-gray-700 text-lg'>
              Maneja un alto volumen de llamadas sin esperas, liberando a tu
              equipo para tareas de mayor valor.
            </p>
          </div>
          <div className='bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-200'>
            <h3 className='text-3xl font-semibold text-purple-700 mb-4'>
              Disponibilidad Constante
            </h3>
            <p className='text-gray-700 text-lg'>
              Atención al cliente 24/7, mejorando la satisfacción y
              accesibilidad sin importar la hora o el día.
            </p>
          </div>
          <div className='bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-200'>
            <h3 className='text-3xl font-semibold text-purple-700 mb-4'>
              Reducción de Errores
            </h3>
            <p className='text-gray-700 text-lg'>
              Procesos estandarizados y precisos para minimizar fallos humanos y
              asegurar la consistencia.
            </p>
          </div>
          <div className='bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-200'>
            <h3 className='text-3xl font-semibold text-purple-700 mb-4'>
              Análisis de Llamadas
            </h3>
            <p className='text-gray-700 text-lg'>
              Obtén insights valiosos de cada interacción para mejorar
              continuamente tus servicios y estrategias.
            </p>
          </div>
          <div className='bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-200'>
            <h3 className='text-3xl font-semibold text-purple-700 mb-4'>
              Integración Flexible
            </h3>
            <p className='text-gray-700 text-lg'>
              Se integra fácilmente con tus sistemas CRM y de gestión
              existentes, optimizando tus flujos de trabajo.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default KeyBenefitsSection;
