@import url('https://fonts.googleapis.com/css2?family=Archivo+Black&family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@400;700;900&family=Poppins:wght@400;700;900&family=Roboto:wght@400;700;900&display=swap');
html {
  scroll-behavior: smooth;
}
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: #1f2937;
}

.clickable-glow:active {
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
}
@layer utilities {
  .bg-instagram-gradient-messages {
    background-image: linear-gradient(to right, #8a2be2, #ff69b4);
  }
  .font-inter {
    font-family: 'Inter', sans-serif;
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out;
  }

  .animate-bubble-rise {
    animation: bubbleRise 10s linear infinite;
  }

  .animate-bubble-rise-slow {
    animation: bubbleRise 15s linear infinite;
  }

  .animate-bubble-rise-fast {
    animation: bubbleRise 8s linear infinite;
  }

  .animate-bubble-rise-delayed {
    animation: bubbleRise 12s linear infinite;
  }

  .animate-bubble-sway {
    animation: bubbleSway 8s ease-in-out infinite;
  }

  .animate-bubble-sway-reverse {
    animation: bubbleSwayReverse 10s ease-in-out infinite;
  }

  .hover\:scale-102:hover {
    transform: scale(1.02);
  }

  .animate-blob {
    animation: blob 7s infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  .animation-delay-6000 {
    animation-delay: 6s;
  }

  .animation-delay-8000 {
    animation-delay: 8s;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bubbleRise {
  0% {
    transform: translateY(100vh) scale(0.8);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100vh) scale(1.2);
    opacity: 0;
  }
}

@keyframes bubbleSway {
  0%,
  100% {
    transform: translateX(0px);
  }
  25% {
    transform: translateX(15px);
  }
  50% {
    transform: translateX(-10px);
  }
  75% {
    transform: translateX(20px);
  }
}

@keyframes bubbleSwayReverse {
  0%,
  100% {
    transform: translateX(0px);
  }
  25% {
    transform: translateX(-20px);
  }
  50% {
    transform: translateX(15px);
  }
  75% {
    transform: translateX(-10px);
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

/* Smooth scrolling for older browsers */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Thin purple scrollbar */
.thin-purple-scrollbar::-webkit-scrollbar {
  width: 5px;
}

.thin-purple-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.thin-purple-scrollbar::-webkit-scrollbar-thumb {
  background: #d8b4fe;
  border-radius: 10px;
}

.thin-purple-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #c084fc;
}

/* **************** Letras inteligencia artificial del HeroSection **************************/

@layer utilities {
  /* --- Clase para el texto azul, ahora con un brillo más suave --- */
  .text-title-glow-blue {
    color: #0044ff;
    text-shadow:
      0 0 15px rgba(0, 77, 160, 0.6),
      0 0 40px rgba(25, 136, 255, 0.4);
  }

  /* --- LA SOLUCIÓN IMPECABLE: SIN ARTEFACTOS --- */
  .text-flawless-gradient {
    position: relative;
    color: transparent;
    background: linear-gradient(
      90deg,
      rgb(0, 177, 142) 0%,
      rgb(51, 126, 169) 25%,
      rgb(102, 91, 198) 50%,
      rgb(154, 48, 179) 75%,
      rgb(205, 5, 255) 100%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow:
      0 0 80px rgb(255, 255, 255),
      0 0 2px rgba(24, 255, 151, 0.38);
  }

  .text-flawless-gradient::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      transparent 0%,
      transparent 10%,
      rgba(255, 255, 255, 0.8) 30%,
      transparent 50%,
      transparent 70%
    );
    background-size: 200% 100%;
    background-position: -100% center;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: shine-sweep 30s ease-in-out infinite;
    animation-delay: 1s;
  }

  /* --- GRADIENTE VERDE COMO EL BOTÓN --- */
  .text-green-gradient {
    position: relative;
    color: transparent;
    background: linear-gradient(90deg, #008f39 0%, #4ade80 50%, #a3e635 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow:
      0 0 80px rgb(255, 255, 255),
      0 0 2px rgba(34, 197, 94, 0.5);
  }

  .text-green-gradient::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      transparent 0%,
      transparent 10%,
      rgba(255, 255, 255, 0.8) 30%,
      transparent 50%,
      transparent 70%
    );
    background-size: 200% 100%;
    background-position: -100% center;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: shine-sweep 30s ease-in-out infinite;
    animation-delay: 1s;
  }

  @keyframes shine-sweep {
    0% {
      background-position: -100% center;
    }
    50% {
      background-position: 100% center;
    }
    100% {
      background-position: -100% center;
    }
  }

  /* --- ESTILOS PARA LÍNEAS DE SUBRAYADO ANIMADAS --- */
  .underline-white-gradient-blue-base {
    position: relative; /* Essential for positioning the ::after pseudo-element */
    display: inline-block; /* Or block, depending on your layout, but inline-block is common for text underlines */
  }

  .underline-white-gradient-blue-base::after {
    content: '';
    position: absolute;
    /* Use 'em' for vertical positioning and height relative to the font-size of the parent */
    bottom: 0.2em; /* Adjust this value to control the distance of the underline from the text. Negative values lift it, positive values lower it. */
    left: 0;
    width: 0; /* Starts at 0, will expand on hover/active */
    height: 0.1em; /* Adjust this value to control the thickness of the underline relative to the text. */
    z-index: -1;
    background: linear-gradient(
      180deg,
      rgba(0, 55, 255, 0) 0%,
      rgb(255, 255, 255, 1) 100%
    );
    border-radius: 0.04em; /* You might want to adjust this relative to height as well, or keep it fixed */
    box-shadow:
      0 0 15px rgba(0, 76, 255, 0.7),
      0 0 40px rgba(0, 85, 255, 0.7);
    transition: width 0.8s ease-out;
    z-index: -1;
  }

  .underline-white-gradient-blue-base.animate-underline-white-gradient-blue::after {
    width: 100%;
  }

  /* Variante para la segunda palabra con delay */
  .underline-white-gradient-blue-animated-delay::after {
    transition-delay: 1s;
  }

  /* --- ESTILOS PARA LÍNEAS DE SUBRAYADO ANIMADAS (AZUL CON SOMBRA BLANCA) --- */
  .underline-blue-gradient-white-base {
    position: relative;
    display: inline-block;
  }

  .underline-blue-gradient-white-base::after {
    content: '';
    position: absolute;
    bottom: 0.2em;
    left: 0;
    width: 0;
    height: 0.1em;
    z-index: -1;
    background: linear-gradient(
      180deg,
      rgba(0, 55, 255, 0) 0%,
      rgb(0, 55, 255, 1) 100%
    ); /* Blue gradient */
    border-radius: 0.04em;
    box-shadow:
      0 0 15px rgba(255, 255, 255, 0.7),
      0 0 40px rgba(255, 255, 255, 0.7); /* White shadow */
    transition: width 0.8s ease-out;
    z-index: -1;
  }

  .underline-blue-gradient-white-base.animate-underline-blue-gradient-white-base::after {
    width: 100%;
  }

  /* --- ESTILO PARA SUBRAYADO ESTÁTICO CON GRADIENTE AZUL (PARA TEXTO OSCURO) --- */
  .static-gradient-underline-blue {
    position: relative;
    display: inline-block;
  }

  .static-gradient-underline-blue::after {
    content: '';
    position: absolute;
    bottom: 0.15em; /* Aumentado para más distancia */
    left: 0;
    width: 100%;
    height: 0.15em; /* Aumentado para más grosor */
    z-index: -1;
    background: linear-gradient(
      180deg,
      rgba(0, 55, 255, 0) 0%,
      rgb(0, 55, 255, 1) 100%
    ); /* Blue gradient */
    border-radius: 0.04em;
    box-shadow:
      0 0 15px rgba(255, 255, 255, 0.7),
      0 0 40px rgba(255, 255, 255, 0.7); /* White shadow */
  }

  /* --- ESTILO PARA SUBRAYADO ESTÁTICO CON GRADIENTE BLANCO (PARA TEXTO CLARO) --- */
  .static-gradient-underline-white {
    position: relative;
    display: inline-block;
  }

  .static-gradient-underline-white::after {
    content: '';
    position: absolute;
    bottom: 0.15em; /* Aumentado para más distancia */
    left: 0;
    width: 100%;
    height: 0.15em; /* Aumentado para más grosor */
    z-index: -1;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0) 0%,
      rgb(255, 255, 255, 1) 100%
    ); /* White gradient */
    border-radius: 0.04em;
    box-shadow:
      0 0 15px rgba(0, 76, 255, 0.7),
      0 0 40px rgba(0, 85, 255, 0.7); /* Blue shadow for contrast */
  }

  /* --- ESTILOS PARA TEXTO CON GRADIENTE AZUL Y GLOW BLANCO --- */
  .text-white-blue-glow {
    color: transparent;
    background: radial-gradient(
      circle,
      rgba(0, 102, 255, 1) 0%,
      rgb(0, 0, 255) 100%
    );
    /* Gradiente azul */
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 40px rgba(255, 255, 255, 0.2);
    /* Resplandor blanco */
  }

  /* **************** BOTÓN ELEGANTE VERDE AZULADO CON GSAP **************************/

  /* Gradiente elegante verde azulado */
  .btn-elegant-gradient {
    background: linear-gradient(
      135deg,
      #0891b2 0%,
      #0ea5e9 25%,
      #06b6d4 50%,
      #14b8a6 75%,
      #10b981 100%
    );
  }

  /* Gradiente para hover más vibrante */
  .btn-elegant-gradient-hover {
    background: linear-gradient(
      135deg,
      #0284c7 0%,
      #0ea5e9 25%,
      #22d3ee 50%,
      #2dd4bf 75%,
      #34d399 100%
    );
  }

  /* **************** ESTILOS PARA SECCIONES DE SERVICIOS **************************/

  /* Animación de flotación para emojis */
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  /* Efecto de pulso para iconos */
  @keyframes pulse-glow {
    0%,
    100% {
      box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
    }
    50% {
      box-shadow:
        0 0 40px rgba(255, 255, 255, 0.6),
        0 0 60px rgba(255, 255, 255, 0.4);
    }
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* Gradientes de fondo suaves para cada sección */
  .service-bg-purple {
    background: linear-gradient(
      135deg,
      rgba(168, 85, 247, 0.05) 0%,
      rgba(236, 72, 153, 0.05) 100%
    );
  }

  .service-bg-blue {
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.05) 0%,
      rgba(6, 182, 212, 0.05) 100%
    );
  }

  .service-bg-green {
    background: linear-gradient(
      135deg,
      rgba(34, 197, 94, 0.05) 0%,
      rgba(20, 184, 166, 0.05) 100%
    );
  }

  .service-bg-orange {
    background: linear-gradient(
      135deg,
      rgba(249, 115, 22, 0.05) 0%,
      rgba(239, 68, 68, 0.05) 100%
    );
  }

  .service-bg-indigo {
    background: linear-gradient(
      135deg,
      rgba(99, 102, 241, 0.05) 0%,
      rgba(168, 85, 247, 0.05) 100%
    );
  }

  .service-bg-yellow {
    background: linear-gradient(
      135deg,
      rgba(245, 158, 11, 0.05) 0%,
      rgba(249, 115, 22, 0.05) 100%
    );
  }

  /* Efectos de texto con glow para títulos de servicios */
  .service-title-glow {
    text-shadow: 0 0 20px currentColor;
  }

  .text-shadow-black {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }

  /* Animación de entrada suave */
  @keyframes slideInFromLeft {
    0% {
      transform: translateX(-100px);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideInFromRight {
    0% {
      transform: translateX(100%);
      opacity: 0;
    }
    100% {
      transform: translateX(-50%);
      opacity: 1;
    }
  }

  .animate-slide-in-left {
    animation: slideInFromLeft 0.8s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInFromRight 0.8s ease-out forwards;
  }

  /* Efecto de brillo en botones de servicios */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%) skewX(-15deg);
    }
    100% {
      transform: translateX(200%) skewX(-15deg);
    }
  }

  .button-shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
    transform: translateX(-100%) skewX(-15deg);
    transition: transform 0.6s;
  }

  .button-shimmer:hover::before {
    animation: shimmer 0.6s ease-out;
  }

  /* === Static CTA Button Styles for Services Section === */

  /* 1. Base styles for all buttons */
  /* --- Estructura Base del Botón --- */
  /* --- Estructura Base del Botón (No tocar) --- */
  .cta-button {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 16px 32px;
    font-size: 18px;
    font-weight: 700;
    border: none;
    border-radius: 50px;
    color: white; /* Color de texto inicial para todos */
    background: transparent;
    overflow: hidden;
    transition: transform 0.3s ease-out;
    z-index: 1;
    border: 1.5px solid transparent;
  }

  /* --- Contenido (No tocar) --- */
  .cta-button > span {
    position: relative;
    z-index: 2;
    transition: color 0.4s ease-in-out;
  }

  /* --- Pseudo-elementos (Estructura, no color) --- */
  .cta-button::before,
  .cta-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50px;
    box-shadow: inset 0px 0px 15px 1px rgb(255, 255, 255);
  }

  /* ::before es el gradiente. El color se define en las clases específicas. */
  .cta-button::before {
    z-index: 0;
  }

  /* ::after es el relleno blanco. Esto es igual para todos. */
  .cta-button::after {
    background: white;
    border-radius: 50px;
    z-index: 1;
    opacity: 0;
    transform: scale(1);
    transition:
      opacity 0.1s ease-out,
      transform 0.1s ease-out;
  }

  /* --- Estados de Hover (Animación, no color) --- */
  .cta-button:hover {
    transform: scale(1);
  }

  .cta-button:hover::after {
    opacity: 1;
    transform: scale(1);
  }
  /* --- Service-Specific Static Styles --- */
  /* --- Clases de Color --- */

  /* Purple Button */
  .cta-button-purple::before {
    background: linear-gradient(to right, #ec4899, #a855f7);
  }
  .cta-button-purple:hover {
    color: #9333ea;
    border: 1.5px solid #9333ea;
  }
  .cta-button-purple:hover > span {
    color: #9333ea;
  }

  /* Blue Button */
  .cta-button-blue::before {
    background: linear-gradient(
      to right,
      rgb(0, 60, 157),
      rgba(0, 98, 255, 1),
      rgb(0, 60, 157)
    );
  }
  .cta-button-blue:hover {
    color: #2563eb;
    border: 1.5px solid #2563eb;
  }

  .cta-button-blue:hover > span {
    color: #2563eb;
  }

  /* Green Button */
  .cta-button-green::before {
    background: linear-gradient(
      to right,
      rgb(3, 80, 0),
      rgba(5, 144, 0, 1),
      rgb(3, 80, 0)
    );
  }
  .cta-button-green:hover {
    color: #16a34a;
    border: 1.5px solid #16a34a;
  }
  .cta-button-green:hover > span {
    color: #16a34a;
  }

  /* Orange Button */
  .cta-button-orange::before {
    background: linear-gradient(to right, #f97316, #ef4444);
  }
  .cta-button-orange:hover {
    color: #ea580c;
    border: 1.5px solid #ea580c;
  }
  .cta-button-orange:hover > span {
    color: #ea580c;
  }

  /* Indigo Button */
  .cta-button-indigo::before {
    background: linear-gradient(to right, #a855f7, #6366f1);
  }
  .cta-button-indigo:hover {
    color: #4f46e5;
    border: 1.5px solid #4f46e5;
  }
  .cta-button-indigo:hover > span {
    color: #4f46e5;
  }

  /* Yellow Button */
  .cta-button-yellow::before {
    background: linear-gradient(to right, #rgb(255, 205, 5), #eab308);
  }
  .cta-button-yellow:hover {
    color: rgb(202, 192, 4);
    border: 1.5px solid rgb(202, 192, 4);
  }
  .cta-button-yellow:hover > span {
    color: rgb(202, 192, 4);
  }
  /* Amberent Button */
  .cta-button-amberent::before {
    background: linear-gradient(to right, #f59e0b, rgb(227, 117, 15));
  }
  .cta-button-amberent:hover {
    color: #d97706;
    border: 1.5px solid #d97706;
  }
  .cta-button-amberent:hover > span {
    color: #d97706;
  }
}

html {
  scroll-padding-top: 100px; /* Adjust this value to match your header's height */
}
