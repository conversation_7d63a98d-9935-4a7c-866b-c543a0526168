import React, { useState, useEffect } from 'react';

interface Section {
  id: string;
  title: string;
}

interface TableOfContentsProps {
  sections: Section[];
}

const TableOfContents: React.FC<TableOfContentsProps> = ({ sections }) => {
  const [activeSection, setActiveSection] = useState<string>('');
  const [isOpen, setIsOpen] = useState(true);

  useEffect(() => {
    const handleScroll = () => {
      const currentPosition = window.scrollY + 100;
      let currentSectionId = '';

      sections.forEach(section => {
        const element = document.getElementById(section.id);
        if (element && element.offsetTop <= currentPosition) {
          currentSectionId = section.id;
        }
      });

      if (currentSectionId) {
        setActiveSection(currentSectionId);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [sections]);

  return (
    <div
      className='py-6 px-4 mt-6 bg-blue-50 rounded-lg
    text-shadow-glow-white
    shadow-inner-glow shadow-inner-glow-white'
    >
      <button
        onClick={() => setIsOpen(!isOpen)}
        className='flex justify-between items-center w-full text-left'
      >
        <h3 className='text-lg font-semibold text-slate-800 '>
          En este artículo
        </h3>
        <svg
          className={`w-5 h-5 text-slate-500 transform transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
          xmlns='http://www.w3.org/2000/svg'
          viewBox='0 0 20 20'
          fill='currentColor'
        >
          <path
            fillRule='evenodd'
            d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z'
            clipRule='evenodd'
          />
        </svg>
      </button>
      <nav className={`mt-4 ${isOpen ? 'block' : 'hidden'}`}>
        <ul className='space-y-2'>
          {sections.map(section => (
            <li key={section.id}>
              <a
                href={`#${section.id}`}
                className={`flex items-center text-sm transition-all duration-200 ease-in-out group ${
                  activeSection === section.id
                    ? 'text-blue-600 font-semibold'
                    : 'text-slate-600 hover:text-slate-900'
                }`}
              >
                <span
                  className={`w-1.5 h-1.5 rounded-full mr-3 transition-all duration-200 ${
                    activeSection === section.id
                      ? 'bg-blue-500 scale-125'
                      : 'bg-blue-300 group-hover:bg-blue-400'
                  }`}
                ></span>
                <span>{section.title}</span>
              </a>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default TableOfContents;
