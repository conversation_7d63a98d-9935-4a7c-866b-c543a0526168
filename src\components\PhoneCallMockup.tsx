import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPhoneVolume,
  faMicrophone,
  faVolumeUp,
  faPhoneSlash,
} from '@fortawesome/free-solid-svg-icons';

interface CallMessage {
  id: number;
  text: string;
  sender: 'customer' | 'restaurant';
}

interface PhoneCallMockupProps {
  startMessagesAnimation: boolean;
  showChatContent: boolean;
  isMobile?: boolean; // New prop
}

const callMessages: CallMessage[] = [
  {
    id: 1,
    text: '<PERSON><PERSON>, me gustaría hacer una reserva para esta noche.',
    sender: 'customer',
  },
  {
    id: 2,
    text: 'C<PERSON><PERSON>, ¿para cuántas personas y a qué hora sería?',
    sender: 'restaurant',
  },
  {
    id: 3,
    text: 'Seríamos cuatro personas, a las 8 de la noche.',
    sender: 'customer',
  },
  {
    id: 4,
    text: 'Un momento por favor... Sí, tenemos una mesa disponible a las 8 para cuatro. ¿A nombre de quién sería la reserva?',
    sender: 'restaurant',
  },
  { id: 5, text: 'A nombre de David.', sender: 'customer' },
  {
    id: 6,
    text: 'Perfecto, Sr. David. Su reserva para cuatro personas a las 8 PM está confirmada. Le enviaremos confirmación por Whatsapp.',
    sender: 'restaurant',
  },
];

export default function PhoneCallMockup({
  startMessagesAnimation,
  showChatContent,
  isMobile = false, // Default to false
}: PhoneCallMockupProps) {
  const messageRefs = useRef<(HTMLDivElement | null)[]>([]);
  const phoneIconRef = useRef<SVGSVGElement>(null);
  const summaryTextRef = useRef<HTMLSpanElement>(null);
  const callingScreenRef = useRef<HTMLDivElement>(null);
  const chatScreenRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (startMessagesAnimation) {
      const ringingDuration = isMobile ? 0.075 : 0.15; // Faster on mobile

      const tl = gsap.timeline();

      // Initial state: calling screen visible, chat screen hidden
      gsap.set(callingScreenRef.current, { opacity: 1 });
      gsap.set(chatScreenRef.current, { opacity: 0 });
      gsap.set(summaryTextRef.current, { opacity: 0 });
      callMessages.forEach((_, index) => {
        gsap.set(messageRefs.current[index], { opacity: 0 });
      });

      // Phase 1: Ringing Phone Animation (loops indefinitely until showChatContent is true)
      tl.to(phoneIconRef.current, {
        rotation: 10,
        yoyo: true,
        repeat: -1, // Repeat indefinitely
        duration: ringingDuration,
        ease: 'power1.inOut',
      });
    }
  }, [startMessagesAnimation, isMobile]);

  // Clear previous GSAP animations
  useEffect(() => {
    const phoneIcon = phoneIconRef.current;
    const callingScreen = callingScreenRef.current;
    const chatScreen = chatScreenRef.current;
    const summaryText = summaryTextRef.current;
    const messages = messageRefs.current;

    return () => {
      gsap.killTweensOf([phoneIcon, callingScreen, chatScreen, summaryText]);
      messages.forEach(el => {
        if (el) {
          gsap.killTweensOf(el);
        }
      });
    };
  }, []);

  useEffect(() => {
    if (showChatContent) {
      const fadeDuration = isMobile ? 0.25 : 0.5;
      const summaryDuration = isMobile ? 0.2 : 0.4;
      const messageDuration = isMobile ? 0.2 : 0.4; // Faster on mobile
      const messageStagger = isMobile ? 0.1 : 0.2; // Faster on mobile

      const tl = gsap.timeline();

      // Stop the ringing animation
      gsap.killTweensOf(phoneIconRef.current);

      // Transition from calling screen to chat screen
      tl.to(callingScreenRef.current, {
        opacity: 0,
        duration: fadeDuration,
        onComplete: () => {
          gsap.set(callingScreenRef.current, { display: 'none' }); // Hide completely after fade out
        },
      })
        .to(
          chatScreenRef.current,
          {
            opacity: 1,
            duration: fadeDuration,
            ease: 'power2.out',
          },
          '-=0.15' // Overlap fade out of calling screen
        ) // Adjusted overlap for faster animation
        .fromTo(
          summaryTextRef.current,
          { opacity: 0, y: 10 },
          { opacity: 1, y: 0, duration: summaryDuration, ease: 'power2.out' },
          '-=0.1' // Overlap slightly with chat screen fade in
        ); // Adjusted overlap for faster animation

      // Phase 3: Messages Appear
      callMessages.forEach((message, index) => {
        tl.fromTo(
          messageRefs.current[index],
          { opacity: 0, y: 10, scale: 0.95 },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: messageDuration,
            ease: 'back.out(1.7)',
          },
          `+=${index * messageStagger}`
        );
      });
    }
  }, [showChatContent, isMobile]);

  /**
   * ## 💡 Resumen de la Lógica
      El contenedor de texto y botones (z-40) queda debajo.
      El contenedor de mockups (z-50) queda arriba, pero con pointer-events-none se vuelve "fantasma" y no bloquea al contenido de abajo.
      Cada mockup individual dentro del contenedor fantasma es "revivido" con pointer-events-auto, permitiendo que solo ellos sean interactivos.
   */

  return (
    <div
      className='relative mx-auto border-gray-800 bg-gray-800 border-[4px] rounded-xl shadow-xl pointer-events-auto w-full max-w-xs transform transition-transform duration-500 hover:scale-105 drop-shadow-glow-forest-green-700-md group cursor-pointer'
      style={{ aspectRatio: '9 / 16' }} // Phone screen aspect ratio
    >
      {/* Altavoz frontal (top) */}
      <div className='w-[30%] h-[5px] bg-black top-[-4px] rounded-t-[1rem] left-1/2 -translate-x-1/2 absolute'></div>
      {/* Power button (left) */}
      <div className='h-[30px] w-[1%] bg-gray-800 absolute -left-[7px] top-[90px] rounded-l-md'></div>
      {/* Volume buttons (left) */}
      <div className='h-[50px] w-[1%] bg-gray-800 absolute -left-[7px] top-[165px] rounded-l-md'></div>
      <div className='h-[50px] w-[1%] bg-gray-800 absolute -left-[7px] top-[220px] rounded-l-md'></div>
      <div className='rounded-lg overflow-hidden w-full h-full'>
        <div className='relative w-full h-full bg-white overflow-hidden'>
          <div
            className={`absolute inset-0 bg-black bg-opacity-50 backdrop-blur-md flex items-center justify-center opacity-0 ${!isMobile ? 'group-hover:opacity-100' : ''} transition-opacity duration-300 z-10 pointer-events-none ${!isMobile ? 'group-hover:pointer-events-auto' : ''}`}
          >
            <h1 className='text-white text-shadow-glow-green-500 text-2xl font-bold text-center pointer-events-none'>
              Ver Demo Asistente de Voz
            </h1>
          </div>
          {/* Calling Screen */}
          <div
            ref={callingScreenRef}
            className='absolute inset-0 flex flex-col items-center justify-start pt-20 bg-gradient-to-br from-teal-800 from-30% to-indigo-300 text-white p-4'
            data-nosnippet='true'
          >
            <div className='w-24 h-24 rounded-full bg-white flex items-center justify-center mb-2'>
              <FontAwesomeIcon
                icon={faPhoneVolume}
                ref={phoneIconRef}
                className='w-16 h-16 text-blue-600'
              />
            </div>
            <span className='text-3xl font-bold mb-2 text-center'>
              Restaurante "El Sabor"
            </span>
            <span className='text-lg text-blue-200'>Llamada en curso...</span>
            <div className='absolute top-[380px]'>
              <div className='w-16 h-16 rounded-full bg-red-500 flex items-center justify-center cursor-pointer'>
                <FontAwesomeIcon
                  icon={faPhoneSlash}
                  className='w-8 h-8 text-white'
                />
              </div>
            </div>
          </div>

          {/* Chat/Summary Screen */}
          <div
            ref={chatScreenRef}
            className='absolute inset-0 flex flex-col'
            role='dialog'
            aria-live='polite'
            aria-label='Contenido del chat de llamada'
            data-nosnippet='true'
          >
            {/* Header - Call Info */}
            <div className='flex flex-col items-center justify-center p-4 bg-gray-900 text-white shadow-md'>
              <FontAwesomeIcon
                icon={faPhoneVolume}
                className='w-8 h-8 text-green-500 mb-2'
              />
              <h1 className='text-xl font-bold text-green-500 text-center mb-1'>
                Asistentes IA para llamadas 24/7
              </h1>
              <span className='text-sm font-semibold' data-nosnippet='true'>
                Resumen llamada del Restaurante "El Sabor"
              </span>
              <span
                ref={summaryTextRef}
                className='text-sm text-gray-400 text-center'
                data-nosnippet='true'
              >
                Reserva de mesa para 4 personas a las 8 PM a nombre de David.
              </span>
              <span className='text-xs text-gray-400'>
                Duración de la llamada: 1:35
              </span>
            </div>

            {/* Call Body - Messages/Transcript */}
            <div
              className='flex-1 p-4 overflow-y-auto'
              style={{ height: 'calc(100% - 120px)' }}
            >
              {callMessages.map((message, index) => (
                <div
                  key={message.id}
                  ref={el => (messageRefs.current[index] = el)}
                  className={`mb-3 flex ${message.sender === 'customer' ? 'justify-end' : 'justify-start'}`}
                  style={{ opacity: 0 }} // Initially hidden
                >
                  <div
                    className={`max-w-[95%] px-4 py-1 rounded-xl text-sm ${
                      message.sender === 'customer'
                        ? 'bg-blue-600 text-white rounded-br-none'
                        : 'bg-gray-600 text-white rounded-bl-none'
                    }`}
                  >
                    {message.text}
                  </div>
                </div>
              ))}
            </div>

            {/* Call Controls */}
            <div className='absolute bottom-0 left-0 right-0 p-4 bg-gray-900 flex justify-around items-center'>
              <div className='flex flex-col items-center text-white'>
                <FontAwesomeIcon icon={faMicrophone} className='w-6 h-6 mb-1' />
                <span className='text-xs'>Silenciar</span>
              </div>
              <div className='flex flex-col items-center text-white'>
                <FontAwesomeIcon icon={faVolumeUp} className='w-6 h-6 mb-1' />
                <span className='text-xs'>Altavoz</span>
              </div>
              <div className='flex flex-col items-center text-red-500'>
                <FontAwesomeIcon icon={faPhoneSlash} className='w-8 h-8 mb-1' />
                <span className='text-xs'>Colgar</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
