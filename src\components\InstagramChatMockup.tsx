import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faComment } from '@fortawesome/free-solid-svg-icons';

import { getCloudImageUrl } from '@/utils/gcs';
import HuggingFaceIcon from './HuggingFaceIcon';

interface Message {
  id: number;
  text?: string;
  sender: 'user' | 'commerce';
  imageUrl?: string;
  isLink?: boolean;
}

interface InstagramChatMockupProps {
  startMessagesAnimation: boolean;
  onAnimationComplete?: () => void;
  isMobile?: boolean; // New prop
  disableInternalLink?: boolean; // New prop to disable internal <a> tag
}

const messages: Message[] = [
  {
    id: 1,
    text: 'Ho<PERSON>, ¿vuestros agentes de chat funcionan en varias plataformas?',
    sender: 'user',
  },
  {
    id: 2,
    text: '¡Hola! Sí, nuestros agentes se integran donde estén tus clientes.',
    sender: 'commerce',
  },
  {
    id: 3,
    sender: 'commerce',
    imageUrl: 'package_chat_integrations.webp',
  },
  {
    id: 4,
    text: 'Genial, necesito automatizar la atención en Instagram y WhatsApp.',
    sender: 'user',
  },
  {
    id: 5,
    text: 'Y si es posible, centralizar los pedidos.',
    sender: 'user',
  },
  {
    id: 6,
    text: 'Hecho. Podemos unificar la comunicación y gestionar pedidos en ambos canales.',
    sender: 'commerce',
  },
  {
    id: 7,
    text: '¿Agendamos una auditoría gratuita y te enseñamos cómo funciona?',
    sender: 'commerce',
  },
  {
    id: 8,
    text: '¡Claro!',
    sender: 'user',
  },
  {
    id: 9,
    text: 'Perfecto. Agenda tu demo gratuita aquí.',
    sender: 'commerce',
    isLink: true,
  },
];

export default function InstagramChatMockup({
  startMessagesAnimation,
  onAnimationComplete,
  isMobile = false, // Default to false
}: InstagramChatMockupProps) {
  const messageRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    if (startMessagesAnimation) {
      const duration = isMobile ? 0.3 : 0.5; // Faster on mobile
      const stagger = isMobile ? 0.15 : 0.25; // Faster stagger on mobile

      const tl = gsap.timeline({
        delay: 0.5, // Delay before first message appears after card is visible
        onComplete: () => {
          if (onAnimationComplete) {
            onAnimationComplete();
          }
        },
      });
      messages.forEach((message, index) => {
        tl.fromTo(
          messageRefs.current[index],
          { opacity: 0, y: 20, scale: 0.95 },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: duration,
            ease: 'power2.out',
          },
          `+=${index === 0 ? 0 : stagger}`
        );
      });
    }
  }, [startMessagesAnimation, onAnimationComplete, isMobile]);

  return (
    <div
      className='relative mx-auto border-gray-800 bg-gray-800 border-[4px] rounded-[2.5rem] shadow-xl pointer-events-auto w-full max-w-xs transform transition-transform duration-500 hover:scale-105 drop-shadow-glow-pink-800-md group cursor-pointer'
      style={{ aspectRatio: '9 / 16' }} // Instagram story/reel aspect ratio
    >
      {/* Altavoz frontal (top) */}
      <div className='w-[120px] h-[5px] bg-black top-[-4px] rounded-t-[1rem] left-1/2 -translate-x-1/2 absolute'></div>
      {/* Power button (right) */}
      <div className='h-[64px] w-[3px] bg-gray-800 absolute -right-[7px] top-[142px] rounded-r-lg'></div>
      {/* Volume buttons (left) */}
      <div className='h-[46px] w-[3px] bg-gray-800 absolute -left-[7px] top-[124px] rounded-l-lg'></div>
      <div className='h-[46px] w-[3px] bg-gray-800 absolute -left-[7px] top-[178px] rounded-l-lg'></div>
      <div className='rounded-[2rem] overflow-hidden w-full h-full'>
        <div className='relative w-full h-full bg-white overflow-hidden'>
          <div
            className={`absolute inset-0 bg-black bg-opacity-50 backdrop-blur-md flex items-center justify-center opacity-0 ${!isMobile ? 'group-hover:opacity-100' : ''} transition-opacity duration-300 z-10 pointer-events-none ${!isMobile ? 'group-hover:pointer-events-auto' : ''}`}
          >
            <h1 className='text-white text-2xl text-shadow-glow-pink-600 font-bold text-center pointer-events-none'>
              Ver Demo Agente de Chat
            </h1>
          </div>
          {/* Header */}
          <div className='flex items-center justify-between p-3 bg-gradient-to-t from-purple-600 to-pink-600 text-white shadow-md'>
            <div className='flex items-center space-x-3 px-2'>
              <div className='w-8 h-8 bg-white rounded-full flex items-center justify-center text-purple-600 font-bold'>
                IA
              </div>
              <span className='text-lg md:text-xl font-semibold text-md text-shadow-glow-black-xl justify-center flex items-center'>
                <HuggingFaceIcon className='mr-2' height='2em' width='2em' />{' '}
                Agente de Chat IA
              </span>
            </div>
            <FontAwesomeIcon icon={faComment} className='w-5 h-5 mr-2' />
          </div>

          {/* Chat Body */}
          <div
            className='flex-1 p-3 overflow-y-auto thin-purple-scrollbar'
            style={{ height: 'calc(100% - 56px)' }}
            data-nosnippet='true'
          >
            {messages.map((message, index) => (
              <div
                key={message.id}
                ref={el => (messageRefs.current[index] = el)}
                className={`mb-2 flex ${
                  message.sender === 'user' ? 'justify-end' : 'justify-start'
                }`}
                style={{ opacity: 0 }} // Initially hidden
              >
                {message.text ? (
                  message.isLink ? (
                    <div
                      className={`max-w-[85%] px-3 py-1.5 rounded-xl text-sm underline ${
                        message.sender === 'user'
                          ? 'bg-instagram-gradient-messages text-white rounded-br-none'
                          : 'bg-gray-200 text-gray-800 rounded-bl-none'
                      }`}
                    >
                      {message.text}
                    </div>
                  ) : (
                    <div
                      className={`max-w-[85%] px-3 py-1.5 rounded-xl text-sm ${
                        message.sender === 'user'
                          ? 'bg-instagram-gradient-messages text-white rounded-br-none'
                          : 'bg-gray-200 text-gray-800 rounded-bl-none'
                      }`}
                    >
                      {message.text}
                    </div>
                  )
                ) : message.imageUrl ? (
                  <div className='max-w-[60%]'>
                    <img
                      src={getCloudImageUrl(message.imageUrl)}
                      alt='Integraciones de chat'
                      className='rounded-xl'
                    />
                  </div>
                ) : null}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
