import React from 'react';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faRocket,
  faComments,
  faUserTie,
} from '@fortawesome/free-solid-svg-icons';
import FondoAnimadoBurbujas from '../components/FondoAnimadoBurbujas';

const Empezar = () => {
  return (
    <div className='relative isolate'>
      <FondoAnimadoBurbujas />
      <div className='min-h-screen'>
        <div className='relative flex items-start justify-center pt-32 px-4 sm:px-6 lg:px-8 overflow-hidden min-h-screen'>
          <div className='relative z-10 flex flex-col w-full mx-auto transition-all duration-500 flex-grow'>
            <div className='flex flex-col transition-all duration-500'>
              <h1 className='text-6xl font-bold text-gray-900 drop-shadow-glow-white-md mb-8 text-center px-4'>
                Elige cómo quieres empezar
              </h1>
              <p className='text-center text-gray-900 text-shadow-glow-white drop-shadow-glow-white mb-4 text-base sm:text-base mb-8'>
                Te ofrecemos distintas vías para descubrir cómo podemos
                transformar tu negocio. Sin presiones, a tu ritmo.
              </p>
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto'>
                <div
                  className='bg-white backdrop-blur-lg p-8 sm:p-12 rounded-3xl flex flex-col items-center text-center
                transition-all duration-300 ease-in-out shadow-lg border-4 border-white hover:border-blue-750 shadow-inner-glow shadow-inner-glow-blue-500-lg drop-shadow-glow-white-lg hover:scale-105'
                >
                  {' '}
                  <FontAwesomeIcon
                    icon={faRocket}
                    className='h-16 w-16 mb-6 text-blue-700 drop-shadow-glow-blue-400'
                  />
                  <h2 className='text-2xl font-bold text-radial-gradient-from-blue-600-to-blue-750 drop-shadow-glow-white mb-2'>
                    Iniciar Prueba Gratuita
                  </h2>
                  <p className='text-gray-900 text-shadow-glow-white drop-shadow-glow-white mb-4 text-base sm:text-base flex-grow'>
                    Explora la plataforma por tu cuenta durante 14 días.
                  </p>
                  <Link
                    to='/activar-prueba'
                    className='group cta-button cta-button-blue animated-border drop-shadow-glow-white hover:drop-shadow-glow-blue w-full mx-auto'
                  >
                    <span className='relative z-10 flex items-center justify-center space-x-2 text-sm sm:text-base'>
                      <FontAwesomeIcon icon={faRocket} className='h-5 w-5' />
                      <span>Activar Prueba</span>
                    </span>
                  </Link>
                </div>
                <div
                  className='bg-white backdrop-blur-lg p-8 sm:p-12 rounded-3xl flex flex-col items-center text-center
                transition-all duration-300 ease-in-out shadow-lg border-4 border-white hover:border-blue-750 shadow-inner-glow shadow-inner-glow-blue-500-lg drop-shadow-glow-white-lg hover:scale-105'
                >
                  <FontAwesomeIcon
                    icon={faComments}
                    className='h-16 w-16 mb-6 text-blue-700 drop-shadow-glow-blue-400'
                  />
                  <h2 className='text-2xl font-bold text-radial-gradient-from-blue-600-to-blue-750 drop-shadow-glow-white mb-2'>
                    Solicitar Demo en Vivo
                  </h2>
                  <p className='text-gray-900 text-shadow-glow-white drop-shadow-glow-white mb-4 text-base sm:text-base flex-grow'>
                    Agenda una sesión guiada con uno de nuestros expertos.
                  </p>
                  <Link
                    to='/demo-gratuita'
                    className='group cta-button cta-button-blue animated-border drop-shadow-glow-white hover:drop-shadow-glow-blue w-full mx-auto'
                  >
                    <span className='relative z-10 flex items-center justify-center space-x-2 text-sm sm:text-base'>
                      <FontAwesomeIcon icon={faComments} className='h-5 w-5' />
                      <span>Agendar Demo</span>
                    </span>
                  </Link>
                </div>
                <div
                  className='bg-white backdrop-blur-lg p-8 sm:p-12 rounded-3xl flex flex-col items-center text-center
                transition-all duration-300 ease-in-out shadow-lg border-4 border-white hover:border-blue-750 shadow-inner-glow shadow-inner-glow-blue-500-lg drop-shadow-glow-white-lg hover:scale-105'
                >
                  {' '}
                  <FontAwesomeIcon
                    icon={faUserTie}
                    className='h-16 w-16 mb-6 text-blue-700 drop-shadow-glow-blue-400'
                  />
                  <h2 className='text-2xl font-bold text-radial-gradient-from-blue-600-to-blue-750 drop-shadow-glow-white mb-2'>
                    Hablar con un Experto
                  </h2>
                  <p className='text-gray-900 text-shadow-glow-white drop-shadow-glow-white mb-4 text-base sm:text-base flex-grow'>
                    Conversemos para encontrar la mejor solución para ti.
                  </p>
                  <Link
                    to='/contacto'
                    className='group cta-button cta-button-blue animated-border drop-shadow-glow-white hover:drop-shadow-glow-blue w-full mx-auto'
                  >
                    <span className='relative z-10 flex items-center justify-center space-x-2 text-sm sm:text-base'>
                      <FontAwesomeIcon icon={faUserTie} className='h-5 w-5' />
                      <span>Contactar</span>
                    </span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Empezar;
