const plugin = require('tailwindcss/plugin');

// --- Lógica para generar animaciones de resplandor dinámicamente ---
const glowColors = {
  'green-600': '#128C7E',
  'green-500': '#008F39',
  'green-400': '#4ADE80',
  'teal-400': '#2dd4bf',
  'teal-500': '#14b8a6',
  'teal-600': '#0d9488',
};

const generatedGlowKeyframes = Object.entries(glowColors).reduce(
  (acc, [name, color]) => {
    acc[`glow-${name}`] = {
      '0%, 100%': {
        boxShadow: `0 0 5px ${color}, 0 0 10px ${color}`,
      },
      '50%': {
        boxShadow: `0 0 15px ${color}, 0 0 25px ${color}`,
      },
    };
    return acc;
  },
  {}
);

const generatedGlowAnimations = Object.keys(glowColors).reduce((acc, name) => {
  acc[`glow-${name}`] = `glow-${name} 3s ease-in-out infinite`;
  return acc;
}, {});

// --- Fin de la lógica de generación ---

export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        inter: ['Inter', 'sans-serif'],
        montserrat: ['Montserrat', 'sans-serif'],
        'archivo-black': ['Archivo Black', 'sans-serif'],
        roboto: ['Roboto', 'sans-serif'],
        poppins: ['Poppins', 'sans-serif'],
      },
      screens: {
        'galaxy-s8plus': '360px',
        'xs-max': { max: '374px' },
        'iphone-se-x-xs': '375px',
        'iphone-12': '390px',
        'iphone-xr-pro-max': { max: '430px' },
        xxs: '300px',
        sm: '640px',
        md: '768px',
        'xl-mockup': '1150px', // New custom breakpoint
        lg: '1024px',
        'xl-custom': '1620px',
      },
      colors: {
        white: '#FFFFFF',
        black: {
          DEFAULT: '#000000',
          900: '#0f111a',
          800: '#161821',
          700: '#1e202a',
          650: '#22242F',
          600: '#252833',
          550: '#292C37',
          500: '#2d303b',
          450: '#31343E',
          400: '#343741',
          300: '#3b3e47',
          200: '#42454e',
          100: '#4b4d55',
        },
        blue: {
          DEFAULT: '#0000ff',
          900: '#01006e',
          850: '#000099',
          800: '#0000cc',
          750: '#0033aa',
          700: '#0022ff',
          650: '#0033FF',
          600: '#0055ff',
          550: '#0066FF',
          500: '#2563EB',
          450: '#4384F6',
          400: '#60A5FA',
          300: '#90CDF4',
        },
        'dark-blue': {
          650: '#00004F',
          600: '#00003f',
          550: '#00004D',
          500: '#00005c',
          450: '#010165',
          400: '#01016e',
        },
        green: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          450: '#36D16F',
          500: '#22c55e',
          550: '#1CB454',
          600: '#16a34a',
          650: '#169143',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        'forest-green': {
          DEFAULT: '#0a4205',
          50: '#f0ffe8',
          100: '#d7ffc0',
          200: '#b8ff97',
          300: '#94ff6d',
          400: '#6efe42',
          450: '#5DF72E',
          500: '#4cff1b',
          550: '#45D516',
          600: '#3daa10',
          650: '#36BC0D',
          700: '#2e9909',
          800: '#207005',
          900: '#1f4a00',
          1000: '#0f3a00',
          1100: '#092700',
          1200: '#051900',
        },
        lime: {
          500: '#A3E635',
        },
        purple: {
          50: '#f5f3ff',
          100: '#ede9fe',
          200: '#ddd6fe',
          300: '#c4b5fd',
          400: '#a78bfa',
          450: '#9974F8',
          500: '#8b5cf6',
          550: '#834DF0',
          600: '#7c3aed',
          650: '#7432E6',
          700: '#6d28d9',
          800: '#5b21b6',
          900: '#4c1d95',
        },
        pink: {
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          450: '#F05DAA',
          500: '#ec4899',
          550: '#E43788',
          600: '#db2777',
          650: '#CD1F67',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
        },
        malva: {
          50: '#f8eafc',
          100: '#f0d9f7',
          200: '#e0b3ed',
          300: '#d08ce3',
          400: '#c066d9',
          450: '#BB44D6',
          500: '#b523d3',
          550: '#A921C7',
          600: '#9e1fba',
          650: '#941DAA',
          700: '#871b9e',
          800: '#701782',
          900: '#591366',
        },
        red: {
          650: '#ED3234',
          600: '#ED1C24',
          550: '#EE2E34',
          500: '#EF4444',
          450: '#F57474',
          400: '#FCA5A5',
        },
        teal: {
          50: '#f0fdfa',
          100: '#ccfbf1',
          200: '#99f6e4',
          300: '#5eead4',
          400: '#2dd4bf',
          450: '#20C6B2',
          500: '#14b8a6',
          550: '#11A697',
          600: '#0d9488',
          650: '#0E857B',
          700: '#0f766e',
          800: '#115e59',
          900: '#134e4a',
        },
        orange: {
          50: '#fff7ed',
          100: '#ffedd5',
          200: '#fed7aa',
          300: '#fdba74',
          400: '#fb923c',
          450: '#FA8229',
          500: '#f97316',
          550: '#F16611',
          600: '#ea580c',
          650: '#D64D0C',
          700: '#c2410c',
          800: '#9a3412',
          900: '#7c2d12',
          1200: '#431407',
        },
        amberent: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          450: '#F8AD17',
          500: '#f59e0b',
          550: '#E78B08',
          600: '#d97706',
          650: '#C76508',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        yellow: {
          50: '#fefce8',
          100: '#fef9c3',
          200: '#fef08a',
          300: '#fde047',
          400: '#facc15',
          450: '#F3BF0E',
          500: '#eab308',
          550: '#DA9F06',
          600: '#ca8a04',
          650: '#B67906',
          700: '#a16207',
          800: '#854d0e',
          900: '#713f12',
        },
        cyan: {
          50: '#ecfeff',
          100: '#cffafe',
          200: '#a5f3fd',
          300: '#67e8f9',
          400: '#22d3ee',
          450: '#14C5E1',
          500: '#06b6d4',
          550: '#07A3C3',
          600: '#0891b2',
          650: '#0B81A1',
          700: '#0e7490',
          800: '#155e75',
          900: '#164e63',
        },
        indigo: {
          50: '#eef2ff',
          100: '#e0e7ff',
          200: '#c7d2fe',
          300: '#a5b4fc',
          400: '#818cf8',
          450: '#7279F5',
          500: '#6366f1',
          550: '#5956EC',
          600: '#4f46e5',
          650: '#493FD2',
          700: '#4338ca',
          800: '#3730a3',
          900: '#312e81',
        },
        violet: {
          50: '#f5f3ff',
          100: '#ede9fe',
          200: '#ddd6fe',
          300: '#c4b5fd',
          400: '#a78bfa',
          450: '#9974F8',
          500: '#8b5cf6',
          550: '#834DF0',
          600: '#7c3aed',
          650: '#7432E6',
          700: '#6d28d9',
          800: '#5b21b6',
          900: '#4c1d95',
        },
      },
      boxShadow: theme => {
        const colors = theme('colors');
        const boxShadows = {};

        const outerGlowSizes = {
          DEFAULT: '10px',
          xxs: '1px',
          xs: '3px',
          sm: '6px',
          md: '15px',
          lg: '30px',
          xl: '45px',
          '2xl': '60px',
        };

        const processColor = (name, colorValue) => {
          Object.entries(outerGlowSizes).forEach(([sizeKey, sizeValue]) => {
            const suffix = sizeKey === 'DEFAULT' ? '' : `-${sizeKey}`;
            boxShadows[`glow-${name}${suffix}`] =
              `0 0 ${sizeValue} ${colorValue}`;
          });
        };

        Object.entries(colors).forEach(([colorName, colorValues]) => {
          if (typeof colorValues === 'object' && colorValues !== null) {
            Object.entries(colorValues).forEach(([shade, colorValue]) => {
              const name =
                shade === 'DEFAULT' ? colorName : `${colorName}-${shade}`;
              processColor(name, colorValue);
            });
          } else {
            processColor(colorName, colorValues);
          }
        });

        return {
          ...boxShadows,
          'glow-green-intensity-1': '0 0 10px 5px rgba(74, 222, 128, 0.7)',
          'glow-green-intensity-2': '0 0 15px 8px rgba(52, 211, 153, 0.7)',
          'glow-green-intensity-3': '0 0 20px 10px rgba(16, 185, 129, 0.7)',
          'glow-green-intensity-4': '0 0 25px 12px rgba(5, 150, 105, 0.7)',
        };
      },
      textShadow: theme => {
        const colors = theme('colors');
        const shadows = {};
        const sizes = {
          DEFAULT: '10px',
          xxs: '1px',
          xs: '3px',
          sm: '6px',
          md: '12px',
          lg: '15px',
          xl: '30px',
          '2xl': '45px',
        };

        Object.entries(colors).forEach(([colorName, colorValues]) => {
          const processColor = (name, colorValue) => {
            Object.entries(sizes).forEach(([sizeKey, sizeValue]) => {
              const suffix = sizeKey === 'DEFAULT' ? '' : `-${sizeKey}`;
              shadows[`glow-${name}${suffix}`] =
                `0 0 ${sizeValue} ${colorValue}`;
              shadows[`glow-${name}${suffix}-opacity-10`] =
                `0 0 ${sizeValue} ${colorValue}1A`;
              shadows[`glow-${name}${suffix}-opacity-20`] =
                `0 0 ${sizeValue} ${colorValue}33`;
              shadows[`glow-${name}${suffix}-opacity-30`] =
                `0 0 ${sizeValue} ${colorValue}4D`;
              shadows[`glow-${name}${suffix}-opacity-40`] =
                `0 0 ${sizeValue} ${colorValue}66`;
              shadows[`glow-${name}${suffix}-opacity-50`] =
                `0 0 ${sizeValue} ${colorValue}80`;
              shadows[`glow-${name}${suffix}-opacity-60`] =
                `0 0 ${sizeValue} ${colorValue}99`;
              shadows[`glow-${name}${suffix}-opacity-70`] =
                `0 0 ${sizeValue} ${colorValue}BB`;
              shadows[`glow-${name}${suffix}-opacity-80`] =
                `0 0 ${sizeValue} ${colorValue}CC`;
              shadows[`glow-${name}${suffix}-opacity-90`] =
                `0 0 ${sizeValue} ${colorValue}DD`;
            });
          };

          if (typeof colorValues === 'object') {
            Object.entries(colorValues).forEach(([shade, colorValue]) => {
              const name =
                shade === 'DEFAULT' ? colorName : `${colorName}-${shade}`;
              processColor(name, colorValue);
            });
          } else {
            processColor(colorName, colorValues);
          }
        });

        return shadows;
      },
      dropShadow: theme => {
        const colors = theme('colors');
        const shadows = {};
        const sizes = {
          DEFAULT: '10px',
          xxs: '1px',
          xs: '3px',
          sm: '6px',
          md: '12px',
          lg: '15px',
          xl: '30px',
          '2xl': '45px',
        };

        // Define offsets for directional shadows
        const directionalOffsets = {
          top: size => `0 -${size}`,
          bottom: size => `0 ${size}`,
          left: size => `-${size} 0`,
          right: size => `${size} 0`,
        };

        Object.entries(colors).forEach(([colorName, colorValues]) => {
          const processColor = (name, colorValue) => {
            Object.entries(sizes).forEach(([sizeKey, sizeValue]) => {
              const suffix = sizeKey === 'DEFAULT' ? '' : `-${sizeKey}`;

              // Symmetrical glow (existing)
              shadows[`glow-${name}${suffix}`] =
                `0 0 ${sizeValue} ${colorValue}`;

              // Directional glows
              Object.entries(directionalOffsets).forEach(
                ([dirKey, offsetFn]) => {
                  const offset = offsetFn(sizeValue);
                  shadows[`glow-${name}-${dirKey}${suffix}`] =
                    `${offset} ${sizeValue} ${colorValue}`;
                }
              );

              // Opacity variations for symmetrical glow (existing)
              shadows[`glow-${name}${suffix}-opacity-10`] =
                `0 0 ${sizeValue} ${colorValue}1A`;
              shadows[`glow-${name}${suffix}-opacity-20`] =
                `0 0 ${sizeValue} ${colorValue}33`;
              shadows[`glow-${name}${suffix}-opacity-30`] =
                `0 0 ${sizeValue} ${colorValue}4D`;
              shadows[`glow-${name}${suffix}-opacity-40`] =
                `0 0 ${sizeValue} ${colorValue}66`;
              shadows[`glow-${name}${suffix}-opacity-50`] =
                `0 0 ${sizeValue} ${colorValue}80`;
              shadows[`glow-${name}${suffix}-opacity-60`] =
                `0 0 ${sizeValue} ${colorValue}99`;
              shadows[`glow-${name}${suffix}-opacity-70`] =
                `0 0 ${sizeValue} ${colorValue}BB`;
              shadows[`glow-${name}${suffix}-opacity-80`] =
                `0 0 ${sizeValue} ${colorValue}CC`;
              shadows[`glow-${name}${suffix}-opacity-90`] =
                `0 0 ${sizeValue} ${colorValue}FF`;

              // Opacity variations for directional glows
              Object.entries(directionalOffsets).forEach(
                ([dirKey, offsetFn]) => {
                  const offset = offsetFn(sizeValue);
                  for (let i = 1; i <= 9; i++) {
                    const opacityHex = Math.round(i * 25.5)
                      .toString(16)
                      .padStart(2, '0')
                      .toUpperCase();
                    shadows[`glow-${name}-${dirKey}${suffix}-opacity-${i}0`] =
                      `${offset} ${sizeValue} ${colorValue}${opacityHex}`;
                  }
                }
              );
            });
          };

          if (typeof colorValues === 'object') {
            Object.entries(colorValues).forEach(([shade, colorValue]) => {
              const name =
                shade === 'DEFAULT' ? colorName : `${colorName}-${shade}`;
              processColor(name, colorValue);
            });
          } else {
            processColor(colorName, colorValues);
          }
        });

        return shadows;
      },
      animation: {
        'fade-in': 'fadeIn 0.8s ease-out',
        'fade-in-up': 'fadeInUp 0.8s ease-out',
        'bubble-rise': 'bubbleRise 10s linear infinite',
        'bubble-rise-slow': 'bubbleRise 15s linear infinite',
        'bubble-rise-fast': 'bubbleRise 8s linear infinite',
        'bubble-rise-delayed': 'bubbleRise 12s linear infinite',
        'bubble-sway': 'bubbleSway 8s ease-in-out infinite',
        'bubble-sway-reverse': 'bubbleSwayReverse 10s ease-in-out infinite',
        'spin-glow': 'spin-glow 1s radial-gradient infinite',
        ...generatedGlowAnimations,
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeInUp: {
          '0%': {
            opacity: '0',
            transform: 'translateY(30px)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        bubbleRise: {
          '0%': {
            transform: 'translateY(20vh) scale(0.8)',
            opacity: '0',
          },
          '5%': {
            opacity: '0.6',
          },
          '95%': {
            opacity: '0.6',
          },
          '100%': {
            transform: 'translateY(-120vh) scale(1.2)',
            opacity: '0',
          },
        },
        bubbleSway: {
          '0%, 100%': {
            transform: 'translateX(0px)',
          },
          '25%': {
            transform: 'translateX(15px)',
          },
          '50%': {
            transform: 'translateX(-10px)',
          },
          '75%': {
            transform: 'translateX(20px)',
          },
        },
        bubbleSwayReverse: {
          '0%, 100%': {
            transform: 'translateX(0px)',
          },
          '25%': {
            transform: 'translateX(-20px)',
          },
          '50%': {
            transform: 'translateX(15px)',
          },
          '75%': {
            transform: 'translateX(-10px)',
          },
        },
        'spin-glow': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        ...generatedGlowKeyframes,
      },
    },
  },
  plugins: [
    plugin(function ({ addUtilities, theme }) {
      const colors = theme('colors');
      const newUtilities = {};

      const innerGlowSizes = {
        DEFAULT: { offset: '10px', blur: '25px' },
        sm: { offset: '8px', blur: '8px' },
        md: { offset: '12px', blur: '12px' },
        lg: { offset: '20px', blur: '20px' },
        xl: { offset: '30px', blur: '30px' },
        '2xl': { offset: '40px', blur: '40px' },
      };

      const directions = {
        top: (o, b) => `inset 0 ${o} ${b} -${o}`,
        bottom: (o, b) => `inset 0 -${o} ${b} -${o}`,
        left: (o, b) => `inset ${o} 0 ${b} -${o}`,
        right: (o, b) => `inset -${o} 0 ${b} -${o}`,
      };

      // Base class to enable composable inner shadows
      newUtilities['.shadow-inner-glow'] = {
        '--tw-shadow-inner-glow-top': '0 0 #0000',
        '--tw-shadow-inner-glow-bottom': '0 0 #0000',
        '--tw-shadow-inner-glow-left': '0 0 #0000',
        '--tw-shadow-inner-glow-right': '0 0 #0000',
        'box-shadow': [
          'var(--tw-shadow-inner-glow-top)',
          'var(--tw-shadow-inner-glow-bottom)',
          'var(--tw-shadow-inner-glow-left)',
          'var(--tw-shadow-inner-glow-right)',
          'var(--tw-ring-offset-shadow, 0 0 #0000)',
          'var(--tw-ring-shadow, 0 0 #0000)',
          'var(--tw-shadow, 0 0 #0000)',
        ].join(', '),
      };

      const processColor = (name, colorValue) => {
        Object.entries(innerGlowSizes).forEach(
          ([sizeKey, { offset, blur }]) => {
            const sizeSuffix = sizeKey === 'DEFAULT' ? '' : `-${sizeKey}`;

            // Generates symmetrical classes for all sides, e.g., .shadow-inner-glow-blue-500-lg
            const symClassName = `.shadow-inner-glow-${name}${sizeSuffix}`;
            newUtilities[symClassName] = {
              '--tw-shadow-inner-glow-top': `${directions.top(
                offset,
                blur
              )} ${colorValue}`,
              '--tw-shadow-inner-glow-bottom': `${directions.bottom(
                offset,
                blur
              )} ${colorValue}`,
              '--tw-shadow-inner-glow-left': `${directions.left(
                offset,
                blur
              )} ${colorValue}`,
              '--tw-shadow-inner-glow-right': `${directions.right(
                offset,
                blur
              )} ${colorValue}`,
            };

            // Generates directional classes for each side, e.g., .shadow-inner-glow-blue-500-top-lg
            Object.entries(directions).forEach(([dirKey, dirFn]) => {
              const dirClassName = `.shadow-inner-glow-${name}-${dirKey}${sizeSuffix}`;
              newUtilities[dirClassName] = {
                [`--tw-shadow-inner-glow-${dirKey}`]: `${dirFn(
                  offset,
                  blur
                )} ${colorValue}`,
              };
            });
          }
        );
      };

      Object.entries(colors).forEach(([colorName, colorValues]) => {
        if (typeof colorValues === 'object' && colorValues !== null) {
          Object.entries(colorValues).forEach(([shade, colorValue]) => {
            const name =
              shade === 'DEFAULT' ? colorName : `${colorName}-${shade}`;
            processColor(name, colorValue);
          });
        } else {
          processColor(colorName, colorValues);
        }
      });

      addUtilities(newUtilities, ['responsive', 'hover']);
    }),
    plugin(function ({ matchUtilities, theme }) {
      matchUtilities(
        {
          'text-shadow': value => ({
            textShadow: value,
          }),
        },
        { values: theme('textShadow') }
      );
    }),
    plugin(function ({ addUtilities, theme }) {
      const glowColors = theme('colors');
      const utilities = Object.entries(glowColors)
        .flatMap(([colorName, colorValues]) => {
          if (typeof colorValues === 'object') {
            return Object.entries(colorValues).map(([shade, colorValue]) => ({
              [`.bg-radial-gradient-glow-${colorName}-${shade}`]: {
                background: `radial-gradient(circle, ${colorValue} 0%, transparent 70%)`,
              },
            }));
          }
          return [];
        })
        .reduce((acc, utility) => ({ ...acc, ...utility }), {});

      addUtilities(utilities, ['responsive', 'hover']);
    }),
    plugin(function ({ addUtilities }) {
      const newUtilities = {
        '.bg-radial-gradient-glow-blue': {
          background:
            'radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%)',
          transition: 'background 0.5s ease-out',
        },
        '.group:hover .bg-radial-gradient-glow-blue': {
          background:
            'radial-gradient(circle at center, rgba(59, 130, 246, 0.2) 0%, transparent 70%)',
        },
      };
      addUtilities(newUtilities, ['responsive', 'hover']);
    }),
    plugin(function ({ addUtilities, theme }) {
      const colors = theme('colors');
      const newUtilities = {};

      Object.entries(colors).forEach(([colorName, colorValues]) => {
        if (typeof colorValues === 'object' && colorValues !== null) {
          const shades = Object.keys(colorValues)
            .filter(key => key !== 'DEFAULT')
            .map(Number)
            .sort((a, b) => a - b);

          Object.entries(colorValues).forEach(([shade, colorValue]) => {
            if (shade === 'DEFAULT') return;

            const currentShade = Number(shade);
            const currentIndex = shades.indexOf(currentShade);

            const prevShade =
              shades[currentIndex - 1] || shades[shades.length - 1];
            const nextShade = shades[currentIndex + 1] || shades[0];

            const prevColor = colorValues[prevShade];
            const nextColor = colorValues[nextShade];

            const gradientColors = [prevColor, colorValue, nextColor].join(
              ', '
            );

            const className = `.text-radial-gradient-fill-${colorName}-${shade}`;
            newUtilities[className] = {
              background: `radial-gradient(circle, ${gradientColors})`,
              '-webkit-background-clip': 'text',
              'background-clip': 'text',
              color: 'transparent',
            };
          });
        }
      });

      addUtilities(newUtilities, ['responsive', 'hover']);
    }),
    plugin(function ({ addUtilities, theme }) {
      const colors = theme('colors');
      const newUtilities = {};

      const colorNames = Object.keys(colors).filter(
        key => typeof colors[key] === 'object' && colors[key] !== null
      );

      colorNames.forEach(color1Name => {
        colorNames.forEach(color2Name => {
          if (color1Name === color2Name) return;

          const shades1 = Object.keys(colors[color1Name])
            .filter(key => key !== 'DEFAULT')
            .map(Number)
            .sort((a, b) => a - b);

          const shades2 = Object.keys(colors[color2Name])
            .filter(key => key !== 'DEFAULT')
            .map(Number)
            .sort((a, b) => a - b);

          const commonShades = shades1.filter(shade => shades2.includes(shade));

          commonShades.forEach(shade => {
            const shadeStr = String(shade);

            const currentShade1Index = shades1.indexOf(shade);
            const prevShade1 =
              shades1[currentShade1Index - 1] || shades1[shades1.length - 1];
            const nextShade1 = shades1[currentShade1Index + 1] || shades1[0];

            const currentShade2Index = shades2.indexOf(shade);
            const prevShade2 =
              shades2[currentShade2Index - 1] || shades2[shades2.length - 1];
            const nextShade2 = shades2[currentShade2Index + 1] || shades2[0];

            const color1Value = colors[color1Name][shadeStr];
            const prevColor1 = colors[color1Name][String(prevShade1)];
            const nextColor1 = colors[color1Name][String(nextShade1)];

            const color2Value = colors[color2Name][shadeStr];
            const prevColor2 = colors[color2Name][String(prevShade2)];
            const nextColor2 = colors[color2Name][String(nextShade2)];

            const gradientColors = [
              color1Value,
              color2Value,
              prevColor1,
              prevColor2,
              nextColor1,
              nextColor2,
              color1Value,
            ]
              .filter(Boolean)
              .join(', ');

            const className = `.text-radial-gradient-blend-${color1Name}-${color2Name}-${shadeStr}`;
            newUtilities[className] = {
              background: `radial-gradient(circle, ${gradientColors})`,
              '-webkit-background-clip': 'text',
              'background-clip': 'text',
              color: 'transparent',
            };
          });
        });
      });

      addUtilities(newUtilities, ['responsive', 'hover']);
    }),
    plugin(function ({ addUtilities, theme }) {
      const colors = theme('colors');
      const flatColors = Object.entries(colors).flatMap(([name, value]) => {
        if (typeof value === 'object' && value !== null) {
          return Object.entries(value).map(([shade, hex]) => {
            const key = shade === 'DEFAULT' ? name : `${name}-${shade}`;
            return { name: key, value: hex };
          });
        }
        return [{ name, value }];
      });

      const utilities = {};
      for (const color1 of flatColors) {
        for (const color2 of flatColors) {
          const className = `.text-radial-gradient-from-${color1.name}-to-${color2.name}`;
          utilities[className] = {
            color: 'transparent',
            background: `radial-gradient(circle, ${color1.value} 0%, ${color2.value} 100%)`,
            '-webkit-background-clip': 'text',
            'background-clip': 'text',
            '-webkit-text-fill-color': 'transparent',
            'text-shadow': '0 0 40px rgba(255, 255, 255, 0.2)',
          };
        }
      }
      addUtilities(utilities, ['responsive', 'hover']);
    }),
  ],
};
