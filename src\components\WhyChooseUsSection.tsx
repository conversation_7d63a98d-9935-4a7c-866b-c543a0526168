import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faLightbulb,
  faChartLine,
  faLifeRing,
  faGraduationCap,
  faUser,
  faBolt,
} from '@fortawesome/free-solid-svg-icons'; // Importar iconos

export default function WhyChooseUsSection() {
  const sectionRef = useRef<HTMLElement>(null);
  const cardsRef = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    const currentSectionRef = sectionRef.current;
    if (!currentSectionRef) return;

    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            gsap.fromTo(
              cardsRef.current,
              { opacity: 0, y: 50, scale: 0.95 },
              {
                opacity: 1,
                y: 0,
                scale: 1,
                duration: 0.8,
                ease: 'power3.out',
                stagger: 0.2,
              }
            );
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.2 } // Trigger when 20% of the section is visible
    );

    observer.observe(currentSectionRef);

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  const features = [
    {
      icon: faGraduationCap,
      title: 'Expertos Titulados',
      description:
        'Contamos con expertos titulados universitarios y con años de experiencia en la creación de software y en la integración y creación de IA.',
    },
    {
      icon: faLightbulb,
      title: 'Innovación Constante',
      description:
        'Estamos a la vanguardia de la inteligencia artificial, ofreciendo soluciones que se adaptan a las últimas tendencias y necesidades del mercado.',
    },
    {
      icon: faChartLine,
      title: 'Resultados Comprobados',
      description:
        'Nuestros clientes experimentan mejoras significativas en eficiencia, reducción de costos y satisfacción del cliente.',
    },
    {
      icon: faLifeRing,
      title: 'Soporte Personalizado',
      description:
        'Ofrecemos un acompañamiento cercano y adaptado a las necesidades específicas de cada negocio, garantizando el éxito de la implementación.',
    },
    {
      icon: faUser,
      title: 'Enfoque en el Cliente',
      description:
        'Nuestras soluciones están diseñadas pensando en las necesidades únicas de cada cliente, asegurando una adaptación perfecta y resultados óptimos.',
    },
    {
      icon: faBolt,
      title: 'Calidad con Velocidad',
      description:
        'Entregamos soluciones de alta calidad de manera ágil y eficiente, optimizando tiempos sin comprometer la excelencia.',
    },
  ];

  return (
    <section
      ref={sectionRef}
      className='relative py-20 bg-gradient-to-b from-white from-20% via-blue-200 via-60% to-white '
    >
      <div className='absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(255,255,255,0)_40%,rgba(255,255,255,1)_80%)]'></div>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative'>
        <h2 className='text-5xl md:text-6xl font-bold tracking-tight text-gray-900 text-center mb-16'>
          ¿Por qué elegirnos?
        </h2>
        <div className='relative -m-px overflow-hidden rounded-2xl'>
          <div className='grid grid-cols-1 md:grid-cols-3'>
            {features.map((feature, index) => (
              <div
                key={index}
                ref={el => (cardsRef.current[index] = el)}
                className='relative p-8 bg-white border border-transparent border-b-blue-200 last:border-b-transparent md:border-r-blue-200 md:[&:nth-child(3n)]:border-r-transparent md:[&:nth-last-child(-n+3)]:border-b-transparent text-center transform transition-all duration-300 hover:drop-shadow-glow-blue-800 hover:border-blue-300
              drop-shadow-glow-blue-500-bottom shadow-inner-glow shadow-inner-glow-blue-100-md'
              >
                <FontAwesomeIcon
                  icon={feature.icon}
                  className='w-12 h-12 text-blue-600 mx-auto mb-6'
                />
                <h3 className='text-2xl font-bold text-gray-700 mb-4'>
                  {feature.title}
                </h3>
                <p className='text-gray-600 leading-relaxed'>
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
