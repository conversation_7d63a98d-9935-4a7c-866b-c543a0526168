import { IconDefinition } from '@fortawesome/fontawesome-svg-core';

export interface ImageConfig {
  url: string;
  enableBackground?: boolean;
  show?: boolean;
}

export interface VisualsConfig {
  showEmoji?: boolean;
  showIcon?: boolean;
}

export interface PageConfig {
  imageConfig?: ImageConfig;
  visualsConfig?: VisualsConfig;
}

export interface Service {
  id: string;
  title: string;
  description: string;
  Icon: IconDefinition;
  emoji: string;
  features: string[];
  link: string;
  cta: string;
  glow: string;
  backgroundGradient: string;
  textColor: string;
  bulletGradient: string;
  buttonClass: string;
  pageConfig?: {
    home?: PageConfig;
    servicios?: PageConfig;
  };
}
