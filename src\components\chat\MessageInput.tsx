import React, { useState, useRef, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPaperPlane,
  faPaperclip,
  faMicrophone,
  faStopCircle,
  faTimes,
  faCheck,
} from '@fortawesome/free-solid-svg-icons';

interface MessageInputProps {
  onSendMessage: (text?: string, audioBlob?: Blob, file?: File) => void;
}

const MessageInput: React.FC<MessageInputProps> = ({ onSendMessage }) => {
  const [text, setText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [recordTime, setRecordTime] = useState(0);
  const [recordedAudioBlob, setRecordedAudioBlob] = useState<Blob | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [recordingError, setRecordingError] = useState<string | null>(null);
  const [audioPreviewUrl, setAudioPreviewUrl] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const recordTimeRef = useRef<number>(0);
  const startTimeRef = useRef<number>(0);

  const handleSend = () => {
    if (text.trim() || selectedFile) {
      onSendMessage(
        text.trim() || undefined,
        undefined,
        selectedFile || undefined
      );
      setText('');
      setSelectedFile(null);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validar tipo y tamaño de archivo
      const maxSize = 10 * 1024 * 1024; // 10MB
      const allowedTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'application/pdf',
        'text/plain',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      ];

      if (!allowedTypes.includes(file.type)) {
        alert(
          'Tipo de archivo no permitido. Solo se permiten imágenes, PDF y documentos de texto.'
        );
        return;
      }

      if (file.size > maxSize) {
        alert('El archivo es demasiado grande. El tamaño máximo es 10MB.');
        return;
      }

      setSelectedFile(file);
    }
    // Limpiar el input para permitir seleccionar el mismo archivo de nuevo
    if (event.target) {
      event.target.value = '';
    }
  };

  const handleFileButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
  };

  // Detectar si es dispositivo móvil
  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const isMobileDevice =
        /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/.test(
          userAgent
        );
      const isTouchDevice =
        'ontouchstart' in window || navigator.maxTouchPoints > 0;
      setIsMobile(isMobileDevice || isTouchDevice);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const startRecording = async () => {
    setRecordingError(null);

    try {
      // Configuración muy simple para móviles Android
      const constraints = isMobile
        ? {
            audio: true, // Configuración más simple posible para móviles
          }
        : {
            audio: {
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true,
              sampleRate: 44100,
              channelCount: 1,
            },
          };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);

      // Configuración muy simple para móviles
      let mimeType = '';
      let options = {};

      if (isMobile) {
        // Para móviles Android, usar configuración mínima sin especificar mimeType
        // Esto permite que el navegador use su configuración por defecto
        options = {};
        mimeType = ''; // Dejar que el navegador decida
      } else {
        // Para desktop, usar la configuración original
        if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
          mimeType = 'audio/webm;codecs=opus';
        } else if (MediaRecorder.isTypeSupported('audio/webm')) {
          mimeType = 'audio/webm';
        } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
          mimeType = 'audio/mp4';
        } else {
          mimeType = 'audio/wav';
        }
        options = { mimeType, audioBitsPerSecond: 128000 };
      }

      // Crear MediaRecorder con configuración mínima para móviles
      if (isMobile) {
        mediaRecorderRef.current = new MediaRecorder(stream); // Sin opciones para móviles
      } else {
        mediaRecorderRef.current = new MediaRecorder(stream, options);
      }

      audioChunksRef.current = [];
      setRecordedAudioBlob(null); // Clear any previous recorded audio

      mediaRecorderRef.current.ondataavailable = event => {
        if (event.data && event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = () => {
        if (audioChunksRef.current.length > 0) {
          // Para móviles, usar tipo genérico, para desktop usar el detectado
          const blobType = isMobile ? 'audio/webm' : mimeType || 'audio/webm';
          const audioBlob = new Blob(audioChunksRef.current, {
            type: blobType,
          });
          setRecordedAudioBlob(audioBlob); // Store the Blob

          // Para móviles, crear URL inmediatamente sin delay
          if (isMobile) {
            const url = URL.createObjectURL(audioBlob);
            setAudioPreviewUrl(url);
          } else {
            // Para desktop, usar el delay original
            setTimeout(() => {
              const url = URL.createObjectURL(audioBlob);
              setAudioPreviewUrl(url);
            }, 100);
          }
        }
        audioChunksRef.current = [];

        // Limpiar referencias de tiempo
        recordTimeRef.current = 0;
        startTimeRef.current = 0;
        setRecordTime(0);

        // Limpiar el stream
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current.onerror = event => {
        console.error('MediaRecorder error:', event);
        setIsRecording(false);
        setRecordedAudioBlob(null);
        stream.getTracks().forEach(track => track.stop());
      };

      // Iniciar grabación - sin timeslice para móviles para máxima compatibilidad
      if (isMobile) {
        // En móviles Android, iniciar sin timeslice para evitar problemas
        mediaRecorderRef.current.start();
      } else {
        mediaRecorderRef.current.start(1000);
      }

      setIsRecording(true);
      setRecordTime(0);
      recordTimeRef.current = 0;
      startTimeRef.current = Date.now();

      // Usar un intervalo más estable que actualice menos frecuentemente
      intervalRef.current = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTimeRef.current) / 1000);
        recordTimeRef.current = elapsed;
        // Solo actualizar el estado si realmente ha cambiado
        setRecordTime(prevTime => {
          if (prevTime !== elapsed) {
            return elapsed;
          }
          return prevTime;
        });
      }, 1000);
    } catch (err) {
      console.error('Error accessing microphone:', err);
      setIsRecording(false);
      setRecordedAudioBlob(null);

      // Mostrar mensaje de error específico para móviles
      let errorMessage = 'Error al acceder al micrófono. Intenta de nuevo.';

      if (err instanceof DOMException) {
        if (err.name === 'NotAllowedError') {
          errorMessage = isMobile
            ? 'Por favor, permite el acceso al micrófono en la configuración de tu navegador o dispositivo.'
            : 'Por favor, permite el acceso al micrófono para grabar audio.';
        } else if (err.name === 'NotFoundError') {
          errorMessage = isMobile
            ? 'No se encontró un micrófono. Verifica que tu dispositivo tenga micrófono habilitado.'
            : 'No se encontró un micrófono en tu dispositivo.';
        } else if (err.name === 'NotSupportedError') {
          errorMessage = isMobile
            ? 'Tu navegador móvil no soporta grabación de audio. Intenta con Chrome o Safari.'
            : 'Tu navegador no soporta grabación de audio.';
        }
      }

      setRecordingError(errorMessage);
      alert(errorMessage);
    }
  };

  const stopRecording = () => {
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === 'recording'
    ) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream
        .getTracks()
        .forEach(track => track.stop()); // Stop microphone access
    }
    setIsRecording(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    // Limpiar referencias
    recordTimeRef.current = 0;
    startTimeRef.current = 0;
  };

  const handleSendAudio = () => {
    if (recordedAudioBlob) {
      onSendMessage(undefined, recordedAudioBlob);
      setRecordedAudioBlob(null);
      if (audioPreviewUrl) {
        URL.revokeObjectURL(audioPreviewUrl);
        setAudioPreviewUrl(null);
      }
    }
  };

  const handleCancelAudio = () => {
    if (recordedAudioBlob) {
      setRecordedAudioBlob(null);
    }
    if (audioPreviewUrl) {
      URL.revokeObjectURL(audioPreviewUrl);
      setAudioPreviewUrl(null);
    }
  };

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      if (
        mediaRecorderRef.current &&
        mediaRecorderRef.current.state !== 'inactive'
      ) {
        mediaRecorderRef.current.stop();
        mediaRecorderRef.current.stream
          .getTracks()
          .forEach(track => track.stop());
      }
      // Limpiar URL del audio
      if (audioPreviewUrl) {
        URL.revokeObjectURL(audioPreviewUrl);
      }
      // Limpiar todas las referencias
      recordTimeRef.current = 0;
      startTimeRef.current = 0;
    };
  }, [audioPreviewUrl]);

  const formatTime = React.useCallback((seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  // Componente memoizado para el display de grabación
  const RecordingDisplay = React.memo(
    ({ time, isMobile }: { time: number; isMobile: boolean }) => (
      <div className='absolute inset-0 flex items-center justify-center bg-red-100 shadow-inner-glow shadow-inner-glow-red-500-sm rounded-lg font-bold text-red-800 text-sm'>
        <div className='flex items-center'>
          <div className='w-3 h-3 bg-red-500 rounded-full animate-pulse mr-2'></div>
          {isMobile ? 'Grabando...' : `Grabando: ${formatTime(time)}`}
          {isMobile && (
            <span className='ml-2 text-xs'>({formatTime(time)})</span>
          )}
        </div>
      </div>
    )
  );

  return (
    <div className='w-full relative bottom-0 left-0 right-0 bg-white rounded-b-3xl p-4'>
      {/* Mensaje de ayuda para móviles */}
      {isMobile && recordingError && (
        <div className='mb-2 p-2 bg-yellow-100 border border-yellow-300 rounded-lg text-xs text-yellow-800'>
          <strong>Consejo:</strong> En dispositivos móviles, asegúrate de
          permitir el acceso al micrófono en la configuración del navegador.
        </div>
      )}

      <div className='flex items-center'>
        <div className='relative flex-1'>
          <textarea
            className='flex-1 p-2 pr-10 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-inner-glow shadow-inner-glow-blue-700-sm w-full resize-none'
            placeholder='Escribe un mensaje...'
            rows={1}
            value={text}
            onChange={e => setText(e.target.value)}
            onKeyDown={e => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSend();
              }
            }}
            disabled={isRecording || !!recordedAudioBlob}
          ></textarea>

          {isRecording && (
            <RecordingDisplay time={recordTime} isMobile={isMobile} />
          )}

          {recordedAudioBlob && (
            <div className='absolute inset-0 flex items-center justify-between bg-blue-400 shadow-inner-glow shadow-inner-glow-white-xl px-6 rounded-lg px-2'>
              {isMobile ? (
                // En móviles, mostrar solo texto sin preview para evitar problemas
                <div className='flex-1 mr-2 py-2 text-white font-medium'>
                  🎤 Audio grabado - Listo para enviar
                </div>
              ) : (
                // En desktop, mostrar preview normal
                audioPreviewUrl && (
                  <audio
                    src={audioPreviewUrl}
                    controls
                    preload='metadata'
                    className='flex-1 mr-2 py-2'
                    style={{ maxWidth: 'calc(100% - 120px)' }}
                  />
                )
              )}
              <button
                onClick={handleSendAudio}
                className='text-green-500 bg-white  rounded-full focus:outline-none transform hover:scale-110 transition-transform duration-300 p-1'
              >
                <FontAwesomeIcon
                  icon={faCheck}
                  size='lg'
                  className='bg-white font-bold rounded-full  px-3 p-1'
                />
              </button>
              <button
                onClick={handleCancelAudio}
                className='text-red-500 bg-white  rounded-full focus:outline-none transform hover:scale-110 transition-transform duration-300 p-1 ml-1'
              >
                <FontAwesomeIcon
                  icon={faTimes}
                  size='lg'
                  className='bg-white rounded-full px-3 p-1'
                />
              </button>
            </div>
          )}

          {selectedFile && (
            <div className='absolute inset-0 flex items-center justify-between bg-green-400 shadow-inner-glow shadow-inner-glow-white-xl px-4 rounded-lg'>
              <div className='flex items-center flex-1 mr-2 min-w-0'>
                <FontAwesomeIcon
                  icon={faPaperclip}
                  className='text-white mr-2 flex-shrink-0'
                />
                <span className='text-white text-sm font-medium truncate max-w-full overflow-hidden'>
                  {selectedFile.name}
                </span>
              </div>
              <button
                onClick={handleSend}
                className='text-green-500 bg-white rounded-full focus:outline-none transform hover:scale-110 transition-transform duration-300 p-1'
              >
                <FontAwesomeIcon
                  icon={faCheck}
                  size='lg'
                  className='bg-white font-bold rounded-full px-3 p-1'
                />
              </button>
              <button
                onClick={handleRemoveFile}
                className='text-red-500 bg-white rounded-full focus:outline-none transform hover:scale-110 transition-transform duration-300 p-1 ml-1'
              >
                <FontAwesomeIcon
                  icon={faTimes}
                  size='lg'
                  className='bg-white rounded-full px-3 p-1'
                />
              </button>
            </div>
          )}

          {!isRecording && !recordedAudioBlob && !selectedFile ? (
            <button
              onMouseDown={isMobile ? undefined : startRecording}
              onTouchStart={isMobile ? startRecording : undefined}
              onClick={isMobile ? startRecording : undefined}
              className={`absolute right-2 top-[40%] -translate-y-1/2 w-6 h-6 flex items-center justify-center focus:outline-none transform hover:scale-110 transition-transform duration-300 ${
                isMobile
                  ? 'text-blue-600 bg-blue-50 rounded-full'
                  : 'text-blue-500'
              }`}
              title={
                isMobile
                  ? 'Toca para grabar audio'
                  : 'Mantén presionado para grabar'
              }
            >
              <FontAwesomeIcon icon={faMicrophone} />
            </button>
          ) : (
            isRecording && (
              <button
                onMouseUp={isMobile ? undefined : stopRecording}
                onTouchEnd={isMobile ? stopRecording : undefined}
                onClick={isMobile ? stopRecording : undefined}
                className='absolute right-2 top-[40%] -translate-y-1/2 w-6 h-6 flex items-center justify-center text-red-500 bg-red-50 rounded-full focus:outline-none transform hover:scale-110 transition-transform duration-300'
                title='Toca para detener grabación'
              >
                <FontAwesomeIcon icon={faStopCircle} />
              </button>
            )
          )}
        </div>
        {!isRecording && !recordedAudioBlob && !selectedFile && (
          <button
            onClick={handleFileButtonClick}
            className='ml-1 mr-1 w-10 h-10 bg-blue-700 rounded-full flex items-center justify-center text-white shadow-inner-glow shadow-inner-glow-white-md focus:outline-none transform hover:scale-110 transition-transform duration-300 self-start'
          >
            <FontAwesomeIcon icon={faPaperclip} />
          </button>
        )}
        {!isRecording && !recordedAudioBlob && !selectedFile && (
          <button
            onClick={handleSend}
            className='w-14 h-14 bg-blue-700 rounded-full flex items-center justify-center text-white shadow-inner-glow shadow-inner-glow-white-md focus:outline-none transform hover:scale-110 transition-transform duration-300 self-start mt-[-0.5rem]'
          >
            <FontAwesomeIcon icon={faPaperPlane} />
          </button>
        )}
      </div>

      {/* Input oculto para selección de archivos */}
      <input
        type='file'
        ref={fileInputRef}
        onChange={handleFileSelect}
        accept='image/*,.pdf,.txt,.doc,.docx'
        style={{ display: 'none' }}
      />
    </div>
  );
};

export default MessageInput;
