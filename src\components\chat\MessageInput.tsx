import React, { useState, useRef, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPaperPlane,
  faPaperclip,
  faMicrophone,
  faStopCircle,
  faTimes,
  faCheck,
} from '@fortawesome/free-solid-svg-icons';

interface MessageInputProps {
  onSendMessage: (text?: string, audioBlob?: Blob) => void;
}

const MessageInput: React.FC<MessageInputProps> = ({ onSendMessage }) => {
  const [text, setText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [recordTime, setRecordTime] = useState(0);
  const [recordedAudioBlob, setRecordedAudioBlob] = useState<Blob | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  const handleSend = () => {
    if (text.trim()) {
      onSendMessage(text);
      setText('');
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      audioChunksRef.current = [];
      setRecordedAudioBlob(null); // Clear any previous recorded audio

      mediaRecorderRef.current.ondataavailable = event => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: 'audio/webm',
        });
        setRecordedAudioBlob(audioBlob); // Store the Blob
        audioChunksRef.current = [];
        setRecordTime(0);
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);
      setRecordTime(0);
      intervalRef.current = setInterval(() => {
        setRecordTime(prevTime => prevTime + 1);
      }, 1000);
    } catch (err) {
      console.error('Error accessing microphone:', err);
      setIsRecording(false);
      setRecordedAudioBlob(null);
    }
  };

  const stopRecording = () => {
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === 'recording'
    ) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream
        .getTracks()
        .forEach(track => track.stop()); // Stop microphone access
    }
    setIsRecording(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  const handleSendAudio = () => {
    if (recordedAudioBlob) {
      onSendMessage(undefined, recordedAudioBlob);
      setRecordedAudioBlob(null);
    }
  };

  const handleCancelAudio = () => {
    if (recordedAudioBlob) {
      setRecordedAudioBlob(null);
    }
  };

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (
        mediaRecorderRef.current &&
        mediaRecorderRef.current.state !== 'inactive'
      ) {
        mediaRecorderRef.current.stop();
        mediaRecorderRef.current.stream
          .getTracks()
          .forEach(track => track.stop());
      }
    };
  }, []);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className='w-full absolute bottom-0 left-0 right-0 bg-white rounded-b-3xl p-4'>
      <div className='flex items-center'>
        <div className='relative flex-1'>
          <textarea
            className='flex-1 p-2 pr-10 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-inner-glow shadow-inner-glow-blue-700-sm w-full resize-none'
            placeholder='Escribe un mensaje...'
            rows={1}
            value={text}
            onChange={e => setText(e.target.value)}
            onKeyPress={e => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSend();
              }
            }}
            disabled={isRecording || recordedAudioBlob}
          ></textarea>

          {isRecording && (
            <div className='absolute inset-0 flex items-center justify-center bg-white shadow-inner-glow shadow-inner-glow-blue-700-sm rounded-lg font-bold text-gray-900 text-sm'>
              Grabando: {formatTime(recordTime)}
            </div>
          )}

          {recordedAudioBlob && (
            <div className='absolute inset-0 flex items-center justify-between bg-blue-400 shadow-inner-glow shadow-inner-glow-white-xl px-6 rounded-lg px-2'>
              <audio
                src={URL.createObjectURL(recordedAudioBlob)}
                controls
                className='flex-1 mr-2 py-2'
              />
              <button
                onClick={handleSendAudio}
                className='text-green-500 bg-white  rounded-full focus:outline-none transform hover:scale-110 transition-transform duration-300 p-1'
              >
                <FontAwesomeIcon
                  icon={faCheck}
                  size='lg'
                  className='bg-white font-bold rounded-full  px-3 p-1'
                />
              </button>
              <button
                onClick={handleCancelAudio}
                className='text-red-500 bg-white  rounded-full focus:outline-none transform hover:scale-110 transition-transform duration-300 p-1 ml-1'
              >
                <FontAwesomeIcon
                  icon={faTimes}
                  size='lg'
                  className='bg-white rounded-full px-3 p-1'
                />
              </button>
            </div>
          )}

          {!isRecording && !recordedAudioBlob ? (
            <button
              onMouseDown={startRecording}
              onTouchStart={startRecording}
              className='absolute right-2 top-[40%] -translate-y-1/2 w-10 h-10 flex items-center justify-center text-blue-500 focus:outline-none transform hover:scale-110 transition-transform duration-300'
            >
              <FontAwesomeIcon icon={faMicrophone} />
            </button>
          ) : (
            isRecording && (
              <button
                onMouseUp={stopRecording}
                onTouchEnd={stopRecording}
                className='absolute right-2 top-[40%] -translate-y-1/2 w-10 h-10 flex items-center justify-center text-red-500 focus:outline-none transform hover:scale-110 transition-transform duration-300'
              >
                <FontAwesomeIcon icon={faStopCircle} />
              </button>
            )
          )}
        </div>
        {!isRecording && !recordedAudioBlob && (
          <button className='ml-1 mr-1 w-10 h-10 bg-blue-700 rounded-full flex items-center justify-center text-white shadow-inner-glow shadow-inner-glow-white-md focus:outline-none transform hover:scale-110 transition-transform duration-300 self-start'>
            <FontAwesomeIcon icon={faPaperclip} />
          </button>
        )}
        {!isRecording && !recordedAudioBlob && (
          <button
            onClick={handleSend}
            className='w-14 h-14 bg-blue-700 rounded-full flex items-center justify-center text-white shadow-inner-glow shadow-inner-glow-white-md focus:outline-none transform hover:scale-110 transition-transform duration-300 self-start mt-[-0.5rem]'
          >
            <FontAwesomeIcon icon={faPaperPlane} />
          </button>
        )}
      </div>
    </div>
  );
};

export default MessageInput;
