import React, { useState, useRef, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlay, faPause } from '@fortawesome/free-solid-svg-icons';
import { getGcsFileUrl } from '../../../utils/gcs';
import AnimatedGreenTrianglesBackground from '../../../components/AnimatedGreenTrianglesBackground';
import { gsap } from 'gsap';

// Datos de ejemplo para idiomas y acentos
const languages = [
  {
    name: 'Español',
    flag: '🇪🇸',
    accents: [
      {
        name: 'España',
        voiceURI: 'Microsoft Helena - Spanish (Spain)',
        lang: 'es-ES',
        audioFile: 'es-ES-Standard-A.mp3',
      },
      {
        name: 'México',
        voiceURI: 'Microsoft Sabina - Spanish (Mexico)',
        lang: 'es-MX',
        audioFile: 'es-MX-Standard-A.mp3',
      },
      {
        name: 'Estados Unidos',
        voiceURI: 'Microsoft Alonso - Spanish (United States)',
        lang: 'es-US',
        audioFile: 'es-US-Standard-A.mp3',
      },
    ],
  },
  {
    name: 'English',
    flag: '🇬🇧',
    accents: [
      {
        name: 'United Kingdom',
        voiceURI: 'Microsoft Libby - English (United Kingdom)',
        lang: 'en-GB',
        audioFile: 'en-GB-Standard-A.mp3',
      },
      {
        name: 'United States',
        voiceURI: 'Microsoft David - English (United States)',
        lang: 'en-US',
        audioFile: 'en-US-Standard-A.mp3',
      },
      {
        name: 'Australia',
        voiceURI: 'Microsoft Catherine - English (Australia)',
        lang: 'en-AU',
        audioFile: 'en-AU-Standard-A.mp3',
      },
    ],
  },
  {
    name: 'Français',
    flag: '🇫🇷',
    accents: [
      {
        name: 'France',
        voiceURI: 'Microsoft Hortense - French (France)',
        lang: 'fr-FR',
        audioFile: 'fr-FR-Standard-A.mp3',
      },
      {
        name: 'Canada',
        voiceURI: 'Microsoft Sylvie - French (Canada)',
        lang: 'fr-CA',
        audioFile: 'fr-CA-Standard-A.mp3',
      },
    ],
  },
];

const LanguageAccentCarousel = () => {
  const [selectedLanguage, setSelectedLanguage] = useState(languages[0]);
  const [selectedAccent, setSelectedAccent] = useState(
    selectedLanguage.accents[0]
  );
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    const bars = document.querySelectorAll('.sound-bar');
    if (isPlaying) {
      bars.forEach((bar: Element) => {
        gsap.to(bar, {
          height: 'random(30, 100)%',
          duration: 'random(0.3, 0.8)',
          ease: 'sine.inOut',
          repeat: -1,
          yoyo: true,
          delay: 'random(0, 0.5)',
        });
      });
    } else {
      bars.forEach((bar: Element) => {
        gsap.killTweensOf(bar);
        gsap.to(bar, { height: '10%', duration: 0.3, ease: 'power1.out' });
      });
    }
  }, [isPlaying]);

  const playSynthesizedVoice = () => {
    if (window.speechSynthesis) {
      window.speechSynthesis.cancel(); // Cancel any previous synthesis before starting a new one

      const utterance = new SpeechSynthesisUtterance(
        'Hola, soy un asistente de voz. Puedes personalizar mi acento y mi idioma.'
      );
      const voices = window.speechSynthesis.getVoices();
      const voice = voices.find(
        v =>
          v.name === selectedAccent.voiceURI && v.lang === selectedAccent.lang
      );

      if (voice) {
        utterance.voice = voice;
      } else {
        const fallbackVoice = voices.find(v =>
          v.lang.startsWith(selectedAccent.lang.split('-')[0])
        );
        if (fallbackVoice) {
          utterance.voice = fallbackVoice;
        }
      }

      utterance.onend = () => setIsPlaying(false);
      setIsPlaying(true); // Set isPlaying to true when synthesis starts
      window.speechSynthesis.speak(utterance);
    }
  };

  const handlePlay = () => {
    if (isPlaying) {
      // If something is playing, stop it
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
        audioRef.current = null; // Clear the ref
      }
      window.speechSynthesis.cancel(); // Cancel any ongoing speech synthesis
      setIsPlaying(false);
    } else {
      // Try to play GCS audio
      const audioUrl = getGcsFileUrl(`sounds/${selectedAccent.audioFile}`);
      const audio = new Audio(audioUrl);
      audioRef.current = audio; // Store the audio element

      audio.addEventListener('canplaythrough', () => {
        audio.play();
        setIsPlaying(true);
      });

      audio.addEventListener('ended', () => {
        setIsPlaying(false);
        audioRef.current = null; // Clear the ref when ended
      });

      audio.addEventListener('error', () => {
        console.error(
          'Error playing audio from GCS, falling back to synthesized voice.'
        );
        audioRef.current = null; // Clear the ref on error
        playSynthesizedVoice(); // Fallback
      });

      // Load the audio to trigger canplaythrough or error
      audio.load();
    }
  };

  const handleLanguageChange = (languageName: string) => {
    const language = languages.find(l => l.name === languageName);
    if (language) {
      setSelectedLanguage(language);
      setSelectedAccent(language.accents[0]);
      if (isPlaying) {
        if (audioRef.current) {
          audioRef.current.pause();
        }
        window.speechSynthesis.cancel(); // Stop any ongoing speech synthesis
        setIsPlaying(false);
      }
    }
  };

  const handleAccentChange = (accentName: string) => {
    const accent = selectedLanguage.accents.find(a => a.name === accentName);
    if (accent) {
      setSelectedAccent(accent);
      if (isPlaying) {
        if (audioRef.current) {
          audioRef.current.pause();
        }
        window.speechSynthesis.cancel(); // Stop any ongoing speech synthesis
        setIsPlaying(false);
      }
    }
  };

  return (
    <section className='relative py-12 md:py-20 overflow-hidden min-h-screen flex items-center justify-center'>
      {/* Animated Green Triangles Background */}
      <AnimatedGreenTrianglesBackground />
      <div className='container mx-auto px-4 relative z-10'>
        <div className='text-center mb-12'>
          <h2 className='text-5xl font-bold text-gray-900'>
            Prueba Nuestras Voces
          </h2>
          <p className='text-lg text-gray-800 mt-4 max-w-3xl mx-auto'>
            Interactúa con nuestro carrusel para escuchar diferentes idiomas y
            acentos. ¡Descubre la voz perfecta para tu asistente!
          </p>
        </div>

        <div className='max-w-4xl mx-auto bg-white backdrop-blur-sm rounded-2xl drop-shadow-glow-green-700-bottom-md shadow-inner-glow shadow-inner-glow-green-100-md p-8'>
          <div className='flex flex-col md:flex-row items-center justify-between'>
            {/* Language Selector */}
            <div className='w-full md:w-1/3 mb-6 md:mb-0'>
              <h3 className='text-xl font-semibold text-gray-800 mb-3'>
                Idioma
              </h3>
              <div className='flex space-x-2'>
                {languages.map(lang => (
                  <button
                    key={lang.name}
                    onClick={() => handleLanguageChange(lang.name)}
                    className={`px-4 py-2 rounded-full text-lg transition-all duration-300 shadow-inner-glow shadow-inner-glow-white-sm hover:bg-blue-700 hover:text-white hover:shadow-inner-glow-white-sm text-shadow-glow-white-sm
                      ${selectedLanguage.name === lang.name ? 'bg-teal-900 text-white' : 'text-green-800 shadow-inner-glow shadow-inner-glow-green-700-sm'}`}
                  >
                    {lang.flag}
                  </button>
                ))}
              </div>
            </div>

            {/* Accent Selector */}
            <div className='w-full md:w-2/3 md:pl-8'>
              <h3 className='text-xl font-semibold text-gren-800 mb-3 text-shadow-glow-white-sm'>
                Acento de {selectedAccent.name}
              </h3>
              <div className='flex flex-wrap gap-2 '>
                {selectedLanguage.accents.map(accent => (
                  <button
                    key={accent.name}
                    onClick={() => handleAccentChange(accent.name)}
                    className={`hover:bg-blue-700 hover:text-white hover:shadow-inner-glow hover:shadow-inner-glow-white shadow-inner-glow shadow-inner-glow-white-sm px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300
                      ${selectedAccent.name === accent.name ? 'bg-green-800 text-white transform scale-105' : 'text-green-800 shadow-inner-glow shadow-inner-glow-green-700-sm'}`}
                  >
                    {accent.name}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Sound Wave and Play Button */}
          <div className='mt-8 flex flex-col items-center'>
            <div className='w-full h-20 bg-white/50 rounded-lg flex items-center justify-center mb-6 overflow-hidden'>
              <div className='flex items-center justify-center w-full h-full'>
                {Array.from({ length: 50 }).map((_, i) => (
                  <div
                    key={i}
                    className='sound-bar w-1 bg-gradient-to-b from-green-500 to-teal-800 rounded-full mx-px transition-all duration-300 shadow-inner-glow shadow-inner-glow-white-xs'
                    style={{
                      height: '10%',
                    }}
                  ></div>
                ))}
              </div>
            </div>

            <button
              onClick={handlePlay}
              className='bg-gradient-to-r from-teal-800 to-green-600 text-white rounded-full w-24 h-24 flex items-center justify-center transform hover:scale-105 transition-all duration-300 focus:outline-none
              drop-shadow-glow-green-700 shadow-inner-glow shadow-inner-glow-white-md
              hover:bg-gradient-to-b hover:from-blue-800 hover:to-blue-600 hover:shadow-inner-glow hover:shadow-inner-glow-white-lg hover:scale-105 hover:drop-shadow-glow-white'
            >
              <FontAwesomeIcon
                icon={isPlaying ? faPause : faPlay}
                className='text-4xl hover:text-white transition-all duration-300 hover:scale-105 text-shadow-glow-white'
              />
            </button>
            <p className='text-gray-700 mt-4 text-sm text-shadow-glow-white-sm'>
              Haz clic para escuchar una muestra con el acento de{' '}
              <span className='font-semibold'>{selectedAccent.name}</span>.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LanguageAccentCarousel;
