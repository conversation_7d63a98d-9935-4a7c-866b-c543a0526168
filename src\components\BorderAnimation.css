@keyframes rotate {
  100% {
    transform: rotate(1turn);
  }
}

@keyframes animated-glow {
  0% {
    box-shadow:
      0 0 15px rgba(74, 222, 128, 0.4),
      0 0 30px rgba(163, 230, 53, 0.2);
  }
  50% {
    box-shadow:
      0 0 30px rgba(74, 222, 128, 0.8),
      0 0 60px rgba(163, 230, 53, 0.6);
  }
  100% {
    box-shadow:
      0 0 15px rgba(74, 222, 128, 0.4),
      0 0 30px rgba(163, 230, 53, 0.2);
  }
}

.animated-border {
  position: relative;
  z-index: 0;
  overflow: hidden;
  padding: 1px;
  border-radius: 0.5rem; /* Corresponds to rounded-lg */
}

.animated-border:hover {
  animation: animated-glow 2s ease-in-out infinite;
}

.animated-border::before {
  content: '';
  position: absolute;
  z-index: -2;
  left: -50%;
  top: -50%;
  width: 200%;
  height: 200%;
  background-color: #1a2327;
  background-repeat: no-repeat;
  background-size:
    50% 50%,
    50% 50%;
  background-position:
    0 0,
    100% 0,
    100% 100%,
    0 100%;
  background-image:
    linear-gradient(#008f39, #128c7e), linear-gradient(#4ade80, #a3e635),
    linear-gradient(#128c7e, #008f39), linear-gradient(#a3e635, #4ade80);
  /* animation: rotate 4s linear infinite; */ /* Animation removed from base state */
}

.animated-border:hover::before {
  /* animation: rotate 4s linear infinite; */
}

.animated-border::after {
  content: '';
  position: absolute;
  z-index: -1;
  left: 2px;
  top: 2px;
  width: calc(100% - 4px);
  height: calc(100% - 4px);
  background: #10b981; /* Corresponds to cta-button-green */
  border-radius: 0.4rem; /* Slightly smaller than the parent */
}
