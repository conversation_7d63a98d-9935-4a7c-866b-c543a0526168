import React, { useState } from 'react';
import HeroSectionIntroduction from './HeroSectionIntroduction';
import InteractiveMockup from './InteractiveMockup';

const introductionSlides = [
  { bgGradient: 'via-purple-200' },
  { bgGradient: 'via-pink-200' },
  { bgGradient: 'via-green-200' },
  { bgGradient: 'via-amber-200' }, // Added for "Estadísticas Detalladas" slide
];

const IntroductionSection: React.FC = () => {
  const [activeSlide, setActiveSlide] = useState(0);

  const currentBg = introductionSlides[activeSlide].bgGradient;

  return (
    <>
      <HeroSectionIntroduction />
      <div
        className={`relative -mt-16 sm:-mt-20 lg:-mt-24 bg-gradient-to-b from-white ${currentBg} to-white transition-all duration-500 ease-in-out`}
      >
        <div className='container mx-auto px-4'>
          <InteractiveMockup onSlideChange={setActiveSlide} />
        </div>
      </div>
    </>
  );
};

export default IntroductionSection;
