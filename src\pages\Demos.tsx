import React from 'react';
import { demos } from '@/constants/demos';
import DemoCard from '@/components/DemoCard';

interface Demo {
  id: string;
  title: string;
  description: string;
  link: string;
  image?: string;
}

// Componente de la página principal de Demos.
// Muestra una lista de demostraciones interactivas de soluciones de IA.
const DemosPage = () => {
  return (
    <div className='bg-white'>
      {/* Sección del encabezado de la página de Demos */}
      <header
        className='bg-gradient-to-b from-white via-blue-600 via-70% to-blue-800 iphone-xr-pro-max:py-4 py-6
      shadow-inner-glow shadow-inner-glow-white-left-2xl shadow-inner-glow-white-right-2xl'
      >
        <div className='max-w-7xl mx-auto px-6 sm:px-6 lg:px-8'>
          {/* Título principal de la página */}
          <h1 className='text-6xl font-bold tracking-xl font-extrabold tracking-tight text-white  drop-shadow-glow-blue-900-lg'>
            Demos Interactivas
          </h1>
          {/* Descripción de la página */}
          <p className='mt-2 text-lg md:text-xl mx-auto text-white  drop-shadow-glow-blue-900-lg'>
            Explora nuestras demos interactivas para ver nuestras soluciones de
            IA en acción.
          </p>
        </div>
      </header>
      {/* Sección principal que contiene las tarjetas de demostración */}
      <main className='iphone-xr-pro-max:py-2 bg-gradient-to-b from-blue-800 from-[150px] via-white via-[300px] to-white shadow-inner-glow shadow-inner-glow-white-left-2xl shadow-inner-glow-white-right-2xl'>
        <div className='max-w-7xl mx-auto py-6 px-6 sm:px-6lg:px-6 bg-white rounded-t-xl drop-shadow-glow-white-md'>
          {/* Contenedor de las tarjetas de demostración */}
          <div className='grid gap-16 lg:grid-cols-2 bg-white'>
            {/* Mapea sobre el array de demos y renderiza una DemoCard para cada una */}
            {demos.map((demo: Demo) => (
              <DemoCard key={demo.id} demo={demo} />
            ))}
          </div>
        </div>
      </main>
    </div>
  );
};

export default DemosPage;
