import React from 'react';
import { Link } from 'react-router-dom';
import { getCloudImageUrl } from '@/utils/gcs';
import { Service } from '@/types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

interface ServiceCardProps {
  service: Service;
  currentPage?: 'home' | 'servicios';
}

const ServiceCard: React.FC<ServiceCardProps> = ({ service, currentPage }) => {
  const pageConfig =
    currentPage === 'servicios'
      ? service.pageConfig?.servicios
      : service.pageConfig?.home;

  const imageConfig = pageConfig?.imageConfig;
  const visualsConfig = pageConfig?.visualsConfig;
  const showImage = imageConfig?.show !== false && imageConfig?.url;
  const imageHasBackground = imageConfig?.enableBackground !== false;

  const imageClasses = imageHasBackground
    ? 'w-full h-full object-cover rounded-sm transition-all duration-300 group-hover:drop-shadow-glow-blue-500'
    : 'w-full h-full object-cover';

  return (
    <Link to={service.link} className='group block'>
      <div className='relative aspect-w-16 aspect-h-9 max-h-80 rounded-2xl overflow-hidden'>
        {showImage ? (
          <img
            className={imageClasses}
            src={getCloudImageUrl(imageConfig.url)}
            alt={service.title}
          />
        ) : (
          <div
            className={`w-full h-full flex items-center justify-center 
              bg-gradient-to-t ${service.bulletGradient} drop-shadow-${service.glow}
              shadow-inner-glow shadow-inner-glow-white-md
              `}
          >
            <div className='relative flex items-center justify-center w-full h-full '>
              {visualsConfig?.showIcon !== false && service.Icon && (
                <FontAwesomeIcon
                  icon={service.Icon}
                  className='h-10 w-10 text-white drop-shadow-glow-black-xl -zl p-2'
                />
              )}
              {visualsConfig?.showEmoji !== false && service.emoji && (
                <span
                  className={`absolute text-5xl drop-shadow-${service.glow}`}
                  style={{
                    bottom: '10%',
                    right: '10%',
                  }}
                >
                  {service.emoji}
                </span>
              )}
            </div>
          </div>
        )}
        {(!showImage || imageHasBackground) && (
          <div className='absolute inset-0 bg-blue-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300'></div>
        )}
        <div className='absolute inset-0 rounded-2xl shadow-inner-glow-white pointer-events-none'></div>
      </div>
      <div className='mt-6 mx-2 sm:mx-5'>
        <h2
          className={`text-2xl font-bold mt-2 ${service.textColor} group-hover:text-blue-500 transition-colors duration-300 group-hover:drop-shadow-glow-blue-500`}
        >
          {service.title}
        </h2>
        <p className='mt-4 text-lg text-gray-600 transition-all duration-300 group-hover:drop-shadow-glow-blue-500'>
          {service.description}
        </p>
      </div>
    </Link>
  );
};

export default ServiceCard;
