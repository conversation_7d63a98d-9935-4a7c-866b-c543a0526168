import { useState, ChangeEvent, useEffect, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowRight, faCalendarAlt } from '@fortawesome/free-solid-svg-icons';
import { AnimatedGreenTrianglesBackground } from '@/components';
import './../components/BorderAnimation.css';
import { useLocation } from 'react-router-dom';

function useIsMobile(breakpoint = 768) {
  const [isMobile, setIsMobile] = useState(window.innerWidth < breakpoint);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < breakpoint);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [breakpoint]);

  return isMobile;
}

function Contact() {
  const firstInputRef = useRef<HTMLInputElement>(null);
  const calendarRef = useRef<HTMLDivElement>(null);
  const [calendarEmail, setCalendarEmail] = useState('<EMAIL>');
  const [isCalendarEmailValid, setIsCalendarEmailValid] = useState(true);
  const [showCalendar, setShowCalendar] = useState(false);
  const isMobile = useIsMobile();
  const location = useLocation();

  useEffect(() => {
    if (showCalendar && calendarRef.current) {
      const topPos =
        calendarRef.current.getBoundingClientRect().top + window.pageYOffset;
      window.scrollTo({
        top: topPos - 200,
        behavior: 'smooth',
      });
    } else if (firstInputRef.current) {
      firstInputRef.current.focus();
    }

    const queryParams = new URLSearchParams(location.search);
    const focusField = queryParams.get('focus');
    const prefillValue = queryParams.get('value');

    if (focusField === 'companyName' && firstInputRef.current) {
      firstInputRef.current.focus();
      if (prefillValue) {
        setFormData(prev => ({ ...prev, companyName: prefillValue }));
      }
    }
  }, [showCalendar, location.search]);

  const [formData, setFormData] = useState({
    companyName: '',
    companyEmail: '',
    implementation: 'Selecciona un servicio',
    budget: 'Selecciona un rango de precios',
  });
  const [formStatus, setFormStatus] = useState<
    'idle' | 'sending' | 'success' | 'error'
  >('idle');

  const filledCount = Object.values(formData).filter(
    value =>
      value !== '' &&
      value !== 'Selecciona un servicio' &&
      value !== 'Selecciona un rango de precios'
  ).length;

  const handleCalendarEmailChange = (e: ChangeEvent<HTMLInputElement>) => {
    const email = e.target.value;
    setCalendarEmail(email);
    const corporateEmailRegex =
      /^[a-zA-Z0-9._%+-]+@(?!gmail\.com|hotmail\.com|yahoo\.com|outlook\.com)[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    setIsCalendarEmailValid(corporateEmailRegex.test(email));
  };

  const handleScheduleClick = () => {
    if (isCalendarEmailValid) {
      setShowCalendar(true);
    }
  };

  const handleInputChange = (
    e: ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormStatus('sending');

    try {
      const response = await fetch(
        'https://n8n-n8n.dxksiz.easypanel.host/webhook/taratic-web/send-form',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-taratic': 'x-taratic-canarias',
          },
          body: JSON.stringify(formData),
        }
      );

      if (response.ok) {
        setFormStatus('success');
        setFormData({
          companyName: '',
          companyEmail: '',
          implementation: 'Selecciona un servicio',
          budget: 'Selecciona un rango de precios',
        });
      } else {
        setFormStatus('error');
        console.error('Form submission failed:', response.statusText);
      }
    } catch (error) {
      setFormStatus('error');
      console.error('Error submitting form:', error);
    }
  };

  const shadowIntensity = [
    '',
    'shadow-glow-green-intensity-1',
    'shadow-glow-green-intensity-2',
    'shadow-glow-green-intensity-3',
    'shadow-glow-green-intensity-4',
  ];

  const cardShadowClass = shadowIntensity[filledCount];

  const baseInputClass =
    'w-full px-3 py-2 sm:py-2 bg-gray-50 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 hover:drop-shadow-glow-forest-green-800 mb-1 drop-shadow-glow-white-sm';

  const calendarUrl = `https://calendar.app.google/iyPPXWKVjEVYAr8eA?prefill_email=${encodeURIComponent(
    calendarEmail
  )}`;

  return (
    <div
      className='relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 overflow-hidden
    bg-gradient-to-b from-white via-forest-green-700 via-30% to-forest-green-900 backdrop-blur-md'
    >
      <AnimatedGreenTrianglesBackground />
      <div
        className={`relative z-10 flex flex-col md:flex-row w-full ${
          showCalendar ? 'max-w-full px-4' : 'max-w-6xl'
        } mx-auto transition-all duration-500 my-8`}
      >
        <div
          className={`bg-white/50 backdrop-blur-lg p-8 sm:p-12 rounded-3xl flex flex-col
            transition-all duration-500 ${cardShadowClass}
            shadow-inner-glow shadow-inner-glow-white-lg
            drop-shadow-glow-white mb-8 md:mb-0 ${
              showCalendar
                ? 'md:w-0 md:p-0 md:opacity-0'
                : 'md:w-1/2 md:opacity-100'
            }`}
        >
          <div
            className={`transition-opacity duration-300 ${
              showCalendar ? 'opacity-0' : 'opacity-100'
            }`}
          >
            <h1 className='text-4xl font-bold text-radial-gradient-from-forest-green-800-to-forest-green-900 drop-shadow-glow-white-sm mb-1 text-center'>
              Hablemos de tu Proyecto
            </h1>
            <p className='text-center text-gray-800 mb-6 sm:mb-1 text-sm sm:text-base'>
              Te responderemos inmediatamente de manera personalizada gracias a
              nuestras automatizaciones.
            </p>
            <form className='space-y-2 sm:space-y-3' onSubmit={handleSubmit}>
              {/* Form fields... */}
              <div>
                <label
                  htmlFor='companyName'
                  className='block text-sm font-semibold text-forest-green-900 mb-2 text-shadow-glow-white-sm'
                >
                  Nombre
                </label>
                <input
                  ref={firstInputRef}
                  type='text'
                  id='companyName'
                  placeholder='Tu Empresa S.L.'
                  className={`${baseInputClass} ${
                    formData.companyName
                      ? 'border-green-700'
                      : 'border-gray-300'
                  }`}
                  onChange={handleInputChange}
                  value={formData.companyName}
                />
              </div>
              <div>
                <label
                  htmlFor='companyEmail'
                  className='block text-sm font-semibold text-forest-green-900 text-shadow-glow-white-sm mb-2'
                >
                  Correo
                </label>
                <input
                  type='email'
                  id='companyEmail'
                  placeholder='<EMAIL>'
                  className={`${baseInputClass} ${
                    formData.companyEmail
                      ? 'border-green-700'
                      : 'border-gray-300'
                  }`}
                  onChange={handleInputChange}
                  value={formData.companyEmail}
                />
              </div>
              <div>
                <label
                  htmlFor='implementation'
                  className='block text-sm font-semibold text-forest-green-900 text-shadow-glow-white-sm mb-2'
                >
                  ¿Qué servicio te interesa?
                </label>
                <select
                  id='implementation'
                  className={`${baseInputClass} ${
                    formData.implementation !== 'Selecciona un servicio'
                      ? 'border-green-700'
                      : 'border-gray-300'
                  }`}
                  onChange={handleInputChange}
                  value={formData.implementation}
                >
                  <option>Selecciona un servicio</option>
                  <option>Asistentes de Voz IA</option>
                  <option>Agentes IA Personalizados</option>
                  <option>Automatización Inteligente</option>
                  <option>Chatbots Multicanal</option>
                  <option>Gestión Inteligente de Correo</option>
                  <option>Webs con IA Integrada</option>
                  <option>Otro</option>
                </select>
              </div>
              <div>
                <label
                  htmlFor='budget'
                  className='block text-sm font-semibold text-forest-green-900 text-shadow-glow-white-sm mb-2'
                >
                  Presupuesto Estimado
                </label>
                <select
                  id='budget'
                  className={`${baseInputClass} ${
                    formData.budget !== 'Selecciona un rango de precios'
                      ? 'border-green-700'
                      : 'border-gray-300'
                  }`}
                  onChange={handleInputChange}
                  value={formData.budget}
                >
                  <option>Selecciona un rango de precios</option>
                  <option>0€ - 500€</option>
                  <option>500€ - 1.000€</option>
                  <option>1.000€ - 3.000€</option>
                  <option>3.000€ - 5.000€</option>
                  <option>5.000€ - 10.000€</option>
                  <option>10.000€ - 20.000€</option>
                  <option>Más de €20.000€</option>
                </select>
              </div>
              <div>
                <button
                  type='submit'
                  className='group cta-button cta-button-green w-full animated-border drop-shadow-glow-white mt-2'
                  disabled={formStatus === 'sending'}
                >
                  <span className='relative z-10 flex items-center justify-center space-x-2'>
                    {formStatus === 'sending'
                      ? 'Enviando...'
                      : 'Enviar Consulta'}
                    <FontAwesomeIcon
                      icon={faArrowRight}
                      className='h-5 w-5 transition-transform duration-300 group-hover:translate-x-3'
                    />
                  </span>
                </button>
                {formStatus === 'success' && (
                  <p className='text-center text-green-600 mt-2'>
                    ¡Mensaje enviado con éxito!
                  </p>
                )}
                {formStatus === 'error' && (
                  <p className='text-center text-red-600 mt-2'>
                    Error al enviar el mensaje. Por favor, inténtalo de nuevo.
                  </p>
                )}
              </div>
            </form>
          </div>
        </div>

        <div
          className={`bg-green/50 backdrop-blur-lg rounded-3xl flex flex-col transition-all duration-500 shadow-inner-glow shadow-inner-glow-white-lg ${
            showCalendar
              ? 'md:w-full p-0 m-0 drop-shadow-glow-white-lg'
              : 'md:w-1/2 p-8 sm:p-12'
          } ${!showCalendar && 'md:ml-8'}`}
        >
          <div
            className={`flex-grow ${
              showCalendar && !isMobile ? 'hidden' : 'block'
            }`}
          >
            <h2 className='text-4xl font-bold text-white text-shadow-glow-forest-green-800-md drop-shadow-glow-green-800 mb-2 text-center'>
              Agenda una Llamada
            </h2>
            <p className='text-center text-gray-100 text-shadow-glow-forest-green-800-md drop-shadow-glow-green-800 mb-4 text-sm sm:text-base'>
              Usa tu correo de empresa para agendar directamente una llamada
            </p>
            <div className='space-y-3 mb-4 max-w-md mx-auto'>
              <div>
                <label
                  htmlFor='calendarEmail'
                  className='block text-sm font-semibold text-white text-shadow-glow-green-800-sm mb-2'
                >
                  Correo de Empresa
                </label>
                <input
                  type='email'
                  id='calendarEmail'
                  placeholder='<EMAIL>'
                  className={`${baseInputClass} ${
                    isCalendarEmailValid
                      ? 'border-green-700'
                      : 'border-gray-300'
                  }`}
                  onChange={handleCalendarEmailChange}
                  value={calendarEmail}
                />
              </div>
              {isMobile ? (
                <a
                  href={calendarUrl}
                  target='_blank'
                  rel='noopener noreferrer'
                  className={`group cta-button cta-button-green w-full animated-border drop-shadow-glow-white mt-2 ${
                    !isCalendarEmailValid && 'opacity-50 cursor-not-allowed'
                  }`}
                  onClick={e => {
                    if (!isCalendarEmailValid) {
                      e.preventDefault();
                    }
                  }}
                >
                  <span className='relative z-10 flex items-center justify-center space-x-2 mt'>
                    <FontAwesomeIcon icon={faCalendarAlt} className='h-5 w-5' />
                    <span>Agendar Reunión</span>
                  </span>
                </a>
              ) : (
                <button
                  type='button'
                  onClick={handleScheduleClick}
                  disabled={!isCalendarEmailValid}
                  className='group cta-button cta-button-green w-full animated-border drop-shadow-glow-white mt-2 disabled:opacity-50 disabled:cursor-not-allowed'
                >
                  <span className='relative z-10 flex items-center justify-center space-x-2'>
                    <FontAwesomeIcon icon={faCalendarAlt} className='h-5 w-5' />
                    <span>Agendar Reunión</span>
                  </span>
                </button>
              )}
            </div>
          </div>
          {showCalendar && !isMobile && (
            <div
              ref={calendarRef}
              className={`flex-grow flex flex-col bg-white rounded-2xl ${showCalendar && !isMobile ? 'p-0 m-0' : 'p-8 sm:p-12'}  h-full`}
            >
              <div className='rounded-lg overflow-hidden flex-grow min-h-screen p-0 m-0'>
                <iframe
                  src={calendarUrl}
                  width='100%'
                  height='100%'
                  frameBorder='0'
                  className='rounded-lg'
                ></iframe>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Contact;
