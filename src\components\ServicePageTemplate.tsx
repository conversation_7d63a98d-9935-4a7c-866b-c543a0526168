import React from 'react';

import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { Service } from '@/types';
import Footer from './Footer';
import Header from './Header';
import { getCloudImageUrl } from '@/constants';
import TrialSection from '../pages/services/agentes-de-chat-ia-multicanal-components/TrialSection';

interface ServicePageTemplateProps {
  service: Service;
}

const ServicePageTemplate: React.FC<ServicePageTemplateProps> = ({
  service,
}) => {
  return (
    <div className='flex flex-col'>
      <Header />
      <main className='flex-grow pt-16'>
        <section className='relative py-16 md:py-24 lg:py-32 overflow-hidden'>
          <div className='absolute inset-0 z-0'>
            <img
              src={getCloudImageUrl('fondo_servicios.webp')}
              alt='Background'
              className='w-full h-full object-cover opacity-30'
            />
            <div className='absolute inset-0 bg-gradient-to-b from-white/80 via-white/90 to-white'></div>
          </div>
          <div className='relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center'>
            <nav className='flex justify-center space-x-2 mb-4 text-sm text-gray-600'>
              <Link to='/' className='hover:underline'>
                Inicio
              </Link>
              <FontAwesomeIcon icon={faChevronRight} className='h-4 w-4' />
              <Link to='/servicios' className='hover:underline'>
                Servicios
              </Link>
              <FontAwesomeIcon icon={faChevronRight} className='h-4 w-4' />
              <span>{service.title}</span>
            </nav>
            <h1 className='text-4xl md:text-5xl lg:text-6xl font-extrabold text-gray-900 leading-tight mb-6 drop-shadow-md'>
              {service.title}
            </h1>
            <p className='text-lg md:text-xl text-gray-700 max-w-3xl mx-auto mb-8'>
              {service.description}
            </p>
            <Link
              to='/demo-gratuita'
              className='inline-block bg-blue-600 text-white text-lg font-semibold px-8 py-3 rounded-full shadow-lg hover:bg-blue-700 transition duration-300 transform hover:scale-105'
            >
              Solicitar Demo Gratuita
            </Link>
          </div>
        </section>

        <section className='py-16 md:py-24 bg-gray-50'>
          <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
            <h2 className='text-3xl md:text-4xl font-bold text-gray-900 text-center mb-12'>
              Características Clave
            </h2>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
              {service.features.map((feature, index) => (
                <div
                  key={index}
                  className='bg-white rounded-lg shadow-md p-6 text-center transform transition duration-300 hover:scale-105 hover:shadow-xl'
                >
                  <p className='text-lg font-medium text-gray-800'>{feature}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className='py-16 md:py-24'>
          <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center'>
            <h2 className='text-3xl md:text-4xl font-bold text-gray-900 mb-8'>
              ¿Listo para transformar tu negocio?
            </h2>
            <p className='text-lg text-gray-700 max-w-3xl mx-auto mb-10'>
              Contáctanos hoy mismo para descubrir cómo{' '}
              {service.title.toLowerCase()} puede impulsar tu eficiencia y
              satisfacción del cliente.
            </p>
            <Link
              to='/contacto'
              className='inline-block bg-green-600 text-white text-lg font-semibold px-8 py-3 rounded-full shadow-lg hover:bg-green-700 transition duration-300 transform hover:scale-105'
            >
              Contactar Ahora
            </Link>
          </div>
        </section>
        <TrialSection />
      </main>
      <Footer />
    </div>
  );
};

export default ServicePageTemplate;
