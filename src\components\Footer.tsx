import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faEnvelope,
  faMapMarkerAlt,
} from '@fortawesome/free-solid-svg-icons';
import {
  faFacebookF,
  faTwitter,
  faLinkedinIn,
  faInstagram,
  faWhatsapp,
} from '@fortawesome/free-brands-svg-icons';
import { Link } from 'react-router-dom';
import { getCloudImageUrl } from '@/constants';

export default function Footer() {
  return (
    <footer
      id='contacto'
      className='bg-white text-gray-800 drop-shadow-glow-white'
    >
      <div className='max-w-7xl mx-auto px-6 sm:px-6 lg:px-8 py-16 '>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>
          {/* Logo y Descripción */}
          <div className='lg:col-span-2'>
            <Link to='/' className='flex items-center space-x-4 mb-4'>
              <img
                src={getCloudImageUrl('LOGO_TARATIC_V8__solo_logo.svg')}
                alt='TARATIC Logo'
                className='h-8 w-8'
              />
              <img
                src={getCloudImageUrl('LOGO_TARATIC_V8__solo_letras.svg')}
                alt='TARATIC'
                className='h-6'
              />
            </Link>
            <p className='text-gray-600 mb-6 max-w-md'>
              Líderes en consultoría de inteligencia artificial, transformando
              negocios con soluciones innovadoras y tecnología de vanguardia.
            </p>
            <div className='flex space-x-4'>
              <a
                href='#'
                className='text-gray-500 hover:text-blue-500 transition-colors'
              >
                <FontAwesomeIcon icon={faFacebookF} className='h-6 w-6' />
              </a>
              <a
                href='#'
                className='text-gray-500 hover:text-blue-500 transition-colors'
              >
                <FontAwesomeIcon icon={faTwitter} className='h-6 w-6' />
              </a>
              <a
                href='#'
                className='text-gray-500 hover:text-blue-500 transition-colors'
              >
                <FontAwesomeIcon icon={faLinkedinIn} className='h-6 w-6' />
              </a>
              <a
                href='#'
                className='text-gray-500 hover:text-blue-500 transition-colors'
              >
                <FontAwesomeIcon icon={faInstagram} className='h-6 w-6' />
              </a>
            </div>
          </div>

          {/* Enlaces Rápidos */}
          <div>
            <h3 className='text-lg font-semibold mb-6 text-gray-900'>
              Enlaces Rápidos
            </h3>
            <ul className='space-y-3'>
              <li>
                <a
                  href='#servicios'
                  className='text-gray-600 hover:text-blue-500 transition-colors'
                >
                  Servicios
                </a>
              </li>
              <li>
                <a
                  href='#'
                  className='text-gray-600 hover:text-blue-500 transition-colors'
                >
                  Casos de Éxito
                </a>
              </li>
              <li>
                <a
                  href='#'
                  className='text-gray-600 hover:text-blue-500 transition-colors'
                >
                  Blog
                </a>
              </li>
              <li>
                <a
                  href='#'
                  className='text-gray-600 hover:text-blue-500 transition-colors'
                >
                  Sobre Nosotros
                </a>
              </li>
              <li>
                <a
                  href='#'
                  className='text-gray-600 hover:text-blue-500 transition-colors'
                >
                  Política de Privacidad
                </a>
              </li>
            </ul>
          </div>

          {/* Contacto */}
          <div>
            <h3 className='text-lg font-semibold mb-6 text-gray-900'>
              Contacto
            </h3>
            <ul className='space-y-4'>
              <li className='flex items-center space-x-3'>
                <FontAwesomeIcon
                  icon={faEnvelope}
                  className='h-5 w-5 text-blue-400'
                />
                <span className='text-gray-600'><EMAIL></span>
              </li>
              <li className='flex items-center space-x-3'>
                <a
                  href='https://wa.me/34828624064?text=Hola, me interesa conocer más sobre sus servicios de IA'
                  target='_blank'
                  rel='noopener noreferrer'
                  className='text-gray-600 hover:text-blue-500 transition-colors flex items-center space-x-3'
                >
                  <FontAwesomeIcon
                    icon={faWhatsapp}
                    className='h-5 w-5 text-blue-400'
                  />
                  <span>🇪🇸 (+34) 828 62 40 64 (Whatsapp)</span>
                </a>
              </li>
              <li className='flex items-start space-x-3'>
                <FontAwesomeIcon
                  icon={faMapMarkerAlt}
                  className='h-5 w-5 text-blue-400 mt-1'
                />
                <span className='text-gray-600'>
                  Santa Lucía de Tirajana,
                  <br />
                  Vecindario, 35110
                  <br />
                  Las Palmas, España
                </span>
              </li>
            </ul>
          </div>
        </div>

        <div className='border-t border-gray-200 mt-12 pt-8'>
          <div className='flex flex-col md:flex-row justify-between items-center'>
            <p className='text-gray-500 text-sm mb-4 md:mb-0'>
              © 2025 taratic.com . Todos los derechos reservados.
            </p>
            <div className='flex space-x-6 text-sm'>
              <a
                href='#'
                className='text-gray-500 hover:text-blue-500 transition-colors'
              >
                Términos de Servicio
              </a>
              <a
                href='#'
                className='text-gray-500 hover:text-blue-500 transition-colors'
              >
                Política de Privacidad
              </a>
              <a
                href='#'
                className='text-gray-500 hover:text-blue-500 transition-colors'
              >
                Cookies
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
