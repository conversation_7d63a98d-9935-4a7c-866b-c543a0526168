import React, { useState } from 'react';

interface FAQItemProps {
  question: string;
  answer: string;
  highlightPhrase: string; // New prop for the phrase to highlight
}

const highlightPhrase = (text: string, phraseToHighlight: string): string => {
  if (!phraseToHighlight) return text;
  const regex = new RegExp(`(${phraseToHighlight})`, 'gi');
  return text.replace(
    regex,
    `<span style="color:rgb(255, 255, 255); background:linear-gradient(90deg,rgba(1, 21, 153, 0.8) 0%, rgba(0, 47, 255, 0.8) 10%, rgba(0, 64, 255, 0.8) 90%,  rgba(0, 26, 195, 0.8) 100%);
    padding: 2px 2px 2px 2px; text-shadow: 0 0 0px rgba(0, 31, 230, 0.6); border-radius: 2px;">$1</span>`
  );
};

const FAQItem: React.FC<FAQItemProps> = ({
  question,
  answer,
  highlightPhrase: phraseToHighlight,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className=''>
      <button
        className='flex justify-between items-center w-full text-left focus:outline-none'
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className='text-lg font-bold text-gray-900'>{question}</h3>
        <svg
          className={`w-6 h-6 transform transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
          fill='none'
          stroke='currentColor'
          viewBox='0 0 24 24'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth='2'
            d='M19 9l-7 7-7-7'
          ></path>
        </svg>
      </button>
      {isOpen && (
        <div
          className='px-6 mt-4 text-gray-800 transition-all duration-300 ease-in-out overflow-hidden'
          style={{ maxHeight: isOpen ? '500px' : '0' }}
        >
          <p
            dangerouslySetInnerHTML={{
              __html: highlightPhrase(answer, phraseToHighlight),
            }}
          ></p>
        </div>
      )}
    </div>
  );
};

interface FAQSectionProps {
  faqs: { question: string; answer: string; highlightPhrase: string }[];
  shadowColorClass?: string;
}

const FAQSection: React.FC<FAQSectionProps> = ({ faqs, shadowColorClass }) => {
  const shadowClass =
    shadowColorClass ||
    'drop-shadow-glow-blue-200-bottom-lg shadow-inner-glow shadow-inner-glow-blue-100-md';

  return (
    <section className='py-16 px-4 sm:px-6 lg:px-12'>
      <div className='max-w-3xl mx-auto'>
        <h2 className='text-3xl font-extrabold text-gray-900 text-center mb-12'>
          FAQS: Preguntas Frecuentes
        </h2>
        <div className='space-y-6'>
          {faqs.map((faq, index) => (
            <div
              key={index}
              className={`bg-white p-6 rounded-2xl shadow-md hover:shadow-xl transition-shadow duration-300 ${shadowClass}`}
            >
              <FAQItem
                question={faq.question}
                answer={faq.answer}
                highlightPhrase={faq.highlightPhrase}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
