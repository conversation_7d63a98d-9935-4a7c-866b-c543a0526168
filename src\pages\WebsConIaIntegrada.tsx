import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { services } from '../constants/services';
import ServicePageTemplate from '../components/ServicePageTemplate';

const WebsConIaIntegrada = () => {
  const service = services.find(s => s.id === 'webs-con-ia-integrada');
  const location = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  if (!service) {
    return <div>Servicio no encontrado.</div>;
  }

  return (
    <>
      <ServicePageTemplate service={service} />
    </>
  );
};

export default WebsConIaIntegrada;
