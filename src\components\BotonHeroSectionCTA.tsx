// ========================================================================
// ARCHIVO: src/components/BotonHeroSectionCTA.tsx
// ========================================================================
//
// Este componente permanece sin cambios, ya es perfecto: autónomo y optimizado.
//
import React from 'react';
import { useRef, useEffect } from 'react';
import gsap from 'gsap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
// React, useEffect, useRef, y gsap ya están importados en el ámbito del archivo.

const BotonHeroSectionCTA = () => {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const shineRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const tl = gsap.timeline({ repeat: -1 });
    tl.to(buttonRef.current, {
      boxShadow: '0 0 40px 5px rgba(8, 178, 113, 0.6)',
      scale: 1.05,
      duration: 2.5,
      ease: 'power2.inOut',
      yoyo: true,
    });
    tl.fromTo(
      shineRef.current,
      { x: '-110%' },
      { x: '110%', duration: 1.5, ease: 'power1.inOut' },
      '-=2.0'
    );
    const ctx = gsap.context(() => {
      gsap.to(buttonRef.current, {
        scale: 1.1,
        duration: 0.3,
        ease: 'back.out(2)',
        yoyo: true,
        repeat: -1,
        repeatDelay: 8,
        delay: 3,
      });
    });
    return () => {
      tl.kill();
      ctx.revert();
    };
  }, []);

  return (
    <button
      ref={buttonRef}
      className='relative inline-flex items-center justify-center rounded-full font-bold text-blue-600 text-base sm:text-lg w-full sm:w-auto overflow-hidden transition-all duration-300 px-8 py-4 
      border-[1.5px] border-blue-600
      before:absolute before:inset-0 before:bg-white before:opacity-100
      group-hover:before:opacity-100 before:transition-opacity before:duration-500 group-hover:after:opacity-100  group-hover:text-green-700 group-hover:border-white group-hover:drop-shadow-glow-white
      after:absolute after:-inset-2 after:rounded-full after:bg-white/10 after:blur-lg after:transition-opacity after:duration-500 after:opacity-70'
      style={{
        background: `linear-gradient(90deg, rgb(0, 0, 0) 10%,rgba(rgba(0, 0, 0, 0.5) rgbrgba(0, 0, 0, 0.49)%, rgb(0, 0, 0) 100%)`,
      }}
    >
      <div
        ref={shineRef}
        className='absolute inset-0'
        style={{
          background:
            'linear-gradient(90deg, transparent 0%, rgb(0, 184, 61) 50%, transparent 100%)',
          borderRadius: 'inherit',
        }}
        aria-hidden='true'
      />
      <span className='relative flex items-center justify-center space-x-2 z-10'>
        <span>Solicitar Demo Gratuita</span>
        <FontAwesomeIcon
          icon={faArrowRight}
          className='h-5 w-5 transition-transform duration-300 group-hover:translate-x-3'
        />
      </span>
    </button>
  );
};

export default BotonHeroSectionCTA;
