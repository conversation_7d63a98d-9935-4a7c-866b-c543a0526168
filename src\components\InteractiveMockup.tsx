import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import useEmblaCarousel from 'embla-carousel-react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHandPointRight } from '@fortawesome/free-solid-svg-icons';
import { getCloudImageUrl } from '@/utils/gcs';

const tailwindColors = {
  'purple-200': '#E0B3ED',
  'pink-200': '#FBCFE8',
  'green-100': '#DCFCE7',
  'amber-200': '#FDE68A',
};

const buttonColors = {
  'purple-200': '#A050B0', // Darker purple
  'pink-200': '#D05090', // Darker pink
  'green-100': '#208040', // Darker green
  'amber-200': '#D08020', // Darker amber
};

const slidesContent = [
  {
    imageUrl: `${getCloudImageUrl('slide_automatizacion_inteligente_taratic.webp')}`,
    bgColor: 'purple-200',
    content: (
      <div className='w-full h-full bg-gradient-to-b from-malva-100 via-white via-30% to-purple-200 flex items-center justify-center p-2 text-center pt-8'>
        <div className='text-center'>
          <p className='text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-malva-600 via-purple-600 to-malva-600'>
            Automatización Inteligente
          </p>
          <p className='mt-4 text-gray-800 leading-2 mb-2'>
            Gestionamos tus chats y llamadas para que tú te centres en crecer.
          </p>
        </div>
      </div>
    ),
  },
  {
    imageUrl: `${getCloudImageUrl('slide_atencion_24_7_taratic.webp')}`,
    bgColor: 'pink-200',
    content: (
      <div className='w-full h-full bg-gradient-to-b from-pink-100 via-white via-30% to-pink-200 flex items-center justify-center p-2 text-center pt-8'>
        <div className='text-center'>
          <p className='text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-pink-700 via-pink-500 to-pink-700'>
            Atención 24/7
          </p>
          <p className='mt-4 text-gray-800 leading-snug mb-2'>
            Tu negocio siempre disponible para tus clientes, a cualquier hora.
            Dia, noche, tarde y lugar
          </p>
        </div>
      </div>
    ),
  },
  {
    imageUrl: `${getCloudImageUrl('slide_integracion_facil_taratic.webp')}`,
    bgColor: 'green-100',
    content: (
      <div className='w-full h-full bg-gradient-to-b from-green-100 via-white via-30% to-green-100 flex items-center justify-center p-2 text-center pt-8'>
        <div className='text-center'>
          <p className='text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-800 via-green-600 to-green-800'>
            Integración Fácil
          </p>
          <p className='mt-4 text-gray-800 leading-snug mb-2'>
            Nos conectamos con tus sistemas actuales sin complicaciones.
          </p>
        </div>
      </div>
    ),
  },
  {
    imageUrl: `${getCloudImageUrl('slide_estadisticas_detalladas_taratic.webp')}`,
    bgColor: 'amber-200',
    content: (
      <div className='w-full h-full bg-gradient-to-b from-amber-100 via-white via-30% to-amber-200 flex flex-col justify-center items-center p-2 text-center pt-8'>
        <div className='text-center'>
          <p className='text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-amber-800 via-amber-600 to-amber-800'>
            Estadísticas Detalladas
          </p>
          <p className='mt-4 text-gray-800 leading-snug mb-2'>
            Consulta tus datos y métricas clave en cualquier momento y con
            detalle.
          </p>
        </div>
      </div>
    ),
  },
];

interface InteractiveMockupProps {
  onSlideChange: (index: number) => void;
}

const InteractiveMockup: React.FC<InteractiveMockupProps> = ({
  onSlideChange,
}) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true });
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);
  const [showHint, setShowHint] = useState(false);

  const scrollTo = useCallback(
    (index: number) => emblaApi && emblaApi.scrollTo(index),
    [emblaApi]
  );

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    const selectedIndex = emblaApi.selectedScrollSnap();
    setSelectedIndex(selectedIndex);
    onSlideChange(selectedIndex);
  }, [emblaApi, setSelectedIndex, onSlideChange]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    setScrollSnaps(emblaApi.scrollSnapList());
    emblaApi.on('select', onSelect);
    emblaApi.on('reInit', onSelect);
    emblaApi.on('pointerDown', () => setShowHint(false)); // Hide hint on interaction
  }, [emblaApi, setScrollSnaps, onSelect]);

  return (
    <div className='relative flex justify-center items-center flex-col overflow-x-hidden'>
      {/*
        NOTA DE SEGURIDAD Y LAYOUT:
        'overflow-x-hidden' se añade a este contenedor principal de InteractiveMockup
        para asegurar que cualquier desbordamiento interno, especialmente de elementos
        posicionados o transformados, se contenga dentro de este componente.
      */}
      <div
        className='embla cursor-grab active:cursor-grabbing overflow-x-hidden'
        /*
          NOTA DE SEGURIDAD Y LAYOUT:
          'overflow-x-hidden' se añade a este contenedor del carrusel Embla
          para asegurar que el contenido del carrusel no cause un desbordamiento
          horizontal en la página principal.
        */
        ref={emblaRef}
        onMouseEnter={() => setShowHint(true)}
        onMouseLeave={() => setShowHint(false)}
      >
        <div className='embla__container mb-8'>
          {slidesContent.map((slide, index) => (
            <div className='embla__slide' key={index}>
              <div className='relative mx-auto border-gray-800 bg-gray-800 border-[8px] rounded-[2rem] h-[650px] w-[320px] shadow-xl'>
                <div className='w-[160px] h-[20px] bg-gray-800 top-0 rounded-b-[1.5rem] left-1/2 -translate-x-1/2 absolute'></div>
                <div className='h-[40px] w-[4px] bg-gray-800 absolute -left-[12px] top-[120px] rounded-l-lg'></div>
                <div className='h-[40px] w-[4px] bg-gray-800 absolute -left-[12px] top-[170px] rounded-l-lg'></div>
                <div className='h-[60px] w-[4px] bg-gray-800 absolute -right-[12px] top-[140px] rounded-r-lg'></div>
                <div className='rounded-[2rem] overflow-hidden w-full h-full'>
                  <div className='flex flex-col h-full'>
                    <div className='flex flex-col h-full'>
                      <div className='flex flex-col justify-center items-center'>
                        {slide.content}
                      </div>
                      <div className='relative w-full flex-grow'>
                        <img
                          src={slide.imageUrl}
                          alt='Slide Image'
                          className='w-full h-full object-cover object-left'
                          style={{
                            boxShadow: `0 -20px 30px -10px ${tailwindColors[slide.bgColor]}, 0 20px 30px -10px ${tailwindColors[slide.bgColor]}`,
                          }}
                        />
                        <Link
                          to='/empezar'
                          className={`absolute bottom-4 left-1/2 -translate-x-1/2 text-white font-bold py-2 px-4 rounded-full text-sm shadow-lg shadow-inner-glow shadow-inner-glow-white-sm hover:shadow-inner-glow-white-xl hover:text-shadow-glow-black`}
                          style={{
                            backgroundColor: buttonColors[slide.bgColor],
                          }}
                        >
                          Empieza Ahora
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {showHint && index === selectedIndex && (
                <div
                  className='absolute top-1/2 left-1/2 -translate-y-1/2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-lg flex items-center space-x-2 text-gray-700 pointer-events-none
                        text-xl sm:text-base md:text-xl lg:text-2xl animate-slide-in-right
                        '
                >
                  <span>Desliza</span>
                  <FontAwesomeIcon
                    icon={faHandPointRight}
                    className='text-2xl sm:text-xl md:text-2xl lg:text-3xl'
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
      <div className='embla__dots'>
        {scrollSnaps.map((_, index) => (
          <button
            key={index}
            onClick={() => scrollTo(index)}
            className={`embla__dot`.concat(
              index === selectedIndex ? ' embla__dot--selected' : ''
            )}
          />
        ))}
      </div>
    </div>
  );
};

export default InteractiveMockup;
