import React from 'react';
import { Link } from 'react-router-dom';

const FinalCTASection = () => {
  return (
    <section className='p-4 pb-4 bg-gray-900 text-white rounded-2xl shadow-inner-glow shadow-inner-glow-white text-center max-w-4xl mx-auto'>
      <h2 className='text-3xl my-8 mb-6 p-2 border-b-2 border-purple-100 font-bold'>
        ¿Listo para transformar tu atención al cliente?
      </h2>
      <p className='mb-6 text-slate-50 max-w-2xl mx-auto'>
        Descubre cómo nuestros agentes de chat IA pueden impulsar tu negocio.
      </p>
      <Link
        to='/demo-gratuita'
        className='inline-block bg-pink-600 mt-4 mb-8 text-shadow-glow-black text-white hover:bg-white hover:text-blue-700 hover:text-shadow-glow-blue-700 drop-shadow-glow-white-md hover:bg-white shadow-inner-glow shadow-inner-glow-white-md font-bold py-3 px-6 rounded-full transition-all duration-300 text-lg'
      >
        Agenda una Demo Gratuita
      </Link>
    </section>
  );
};

export default FinalCTASection;
