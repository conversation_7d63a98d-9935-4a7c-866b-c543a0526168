import React, { useEffect, useRef } from 'react';
import MessageItem from './MessageItem';

interface Message {
  id: number;
  text?: string;
  sender: 'bot' | 'user';
  audioUrl?: string;
}

interface MessageListProps {
  messages: Message[];
  isTyping: boolean;
  keyboardHeight: number;
}

const MessageList: React.FC<MessageListProps> = ({ messages, isTyping, keyboardHeight }) => {
  const messagesEndRef = useRef<null | HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping, keyboardHeight]);

  return (
    <div className='flex-1 p-4 overflow-y-auto' style={{ paddingBottom: `${keyboardHeight}px` }}>
      {messages.map(message => (
        <MessageItem key={message.id} message={message} />
      ))}
      {isTyping && (
        <div className='my-2 p-3 rounded-lg max-w-xs bg-gray-100 text-blue-800'>
          <div className='flex items-center'>
            <span className='w-2 h-2 bg-gray-500 rounded-full animate-pulse mr-1'></span>
            <span className='w-2 h-2 bg-gray-500 rounded-full animate-pulse mr-1 delay-150'></span>
            <span className='w-2 h-2 bg-gray-500 rounded-full animate-pulse delay-300'></span>
          </div>
        </div>
      )}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
