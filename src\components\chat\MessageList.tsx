import React, { useEffect, useRef } from 'react';
import MessageItem from './MessageItem';

interface Message {
  id: number;
  text?: string;
  sender: 'bot' | 'user';
  audioBlob?: Blob;
  file?: File;
}

interface MessageListProps {
  messages: Message[];
  isTyping: boolean;
  keyboardHeight: number;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  isTyping,
  keyboardHeight,
}) => {
  const messagesEndRef = useRef<null | HTMLDivElement>(null);
  const containerRef = useRef<null | HTMLDivElement>(null);

  const scrollToBottom = (force = false) => {
    if (force) {
      // Scroll inmediato para casos urgentes
      messagesEndRef.current?.scrollIntoView({ behavior: 'auto' });
    } else {
      // Scroll suave normal
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Scroll cuando cambian los mensajes
  useEffect(() => {
    scrollToBottom();

    // Scroll adicional después de un delay para imágenes que pueden tardar en cargar
    const timeoutId = setTimeout(() => {
      scrollToBottom();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [messages, isTyping, keyboardHeight]);

  // Observer para detectar cuando se cargan las imágenes
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const images = container.querySelectorAll('img');
    let loadedImages = 0;
    const totalImages = images.length;

    if (totalImages === 0) return;

    const handleImageLoad = () => {
      loadedImages++;
      scrollToBottom();

      // Cuando todas las imágenes se han cargado, hacer un scroll final
      if (loadedImages === totalImages) {
        setTimeout(() => scrollToBottom(), 100);
      }
    };

    images.forEach(img => {
      if (img.complete) {
        handleImageLoad();
      } else {
        img.addEventListener('load', handleImageLoad);
        img.addEventListener('error', handleImageLoad); // También manejar errores
      }
    });

    return () => {
      images.forEach(img => {
        img.removeEventListener('load', handleImageLoad);
        img.removeEventListener('error', handleImageLoad);
      });
    };
  }, [messages]);

  return (
    <div
      ref={containerRef}
      className='flex-1 px-4 overflow-y-auto'
      style={{ paddingBottom: `${keyboardHeight}px` }}
    >
      {messages.map(message => (
        <MessageItem key={message.id} message={message} />
      ))}
      {isTyping && (
        <div className='my-0 p-3 rounded-lg max-w-xs bg-gray-100 text-blue-800'>
          <div className='flex items-center'>
            <span className='w-2 h-2 bg-gray-500 rounded-full animate-pulse mr-1'></span>
            <span className='w-2 h-2 bg-gray-500 rounded-full animate-pulse mr-1 delay-150'></span>
            <span className='w-2 h-2 bg-gray-500 rounded-full animate-pulse delay-300'></span>
          </div>
        </div>
      )}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
