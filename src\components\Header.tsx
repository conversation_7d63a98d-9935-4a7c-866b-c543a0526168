import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faBars,
  faTimes,
  faChevronDown,
} from '@fortawesome/free-solid-svg-icons';
import { getCloudImageUrl } from '@/constants';
import { services } from '@/constants/services';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isMobileServicesOpen, setisMobileServicesOpen] = useState(false);

  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isMenuOpen]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
    if (isMobileServicesOpen) {
      setisMobileServicesOpen(false);
    }
  };

  const toggleMobileServices = () => {
    setisMobileServicesOpen(!isMobileServicesOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
    setisMobileServicesOpen(false);
  };

  const handleScrollToTopClick = () => {
    window.scrollTo(0, 0);
  };

  return (
    <header
      className={`main-header w-full z-50 fixed bg-white/60 backdrop-blur-md top-0 fixed shadow-inner-glow shadow-inner-glow-white ${
        // Alias: z-index-header-always-on-top
        isMenuOpen ? 'h-screen' : ''
      }`}
    >
      <div className='max-w-[1300px] mx-auto px-6 sm:px-4 lg:px-8'>
        <div className='flex items-center justify-between h-16'>
          <div className='flex items-center space-x-4 ml-1'>
            <Link to='/' className='flex items-center space-x-4'>
              <img
                src={getCloudImageUrl('LOGO_TARATIC_V8__solo_logo.svg')}
                alt='TARATIC Logo'
                className='h-8 w-8 drop-shadow-glow-white'
              />
              <img
                src={getCloudImageUrl('LOGO_TARATIC_V8__solo_letras.svg')}
                alt='TARATIC'
                className='h-6 drop-shadow-glow-white'
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className='hidden md:flex items-center space-x-4'>
            <div className='relative group'>
              <Link
                to='/servicios'
                onClick={handleScrollToTopClick}
                className='flex flex-items-center space-x-2
                text-sm font-medium p-2 px-3 px-3 rounded-full
                text-blue-700 text-shadow-glow-white drop-shadow-glow-white
                hover:shadow-inner-glow hover:shadow-inner-glow-white-sm hover:text-shadow-glow-white hover:text-white hover:bg-blue-700 hover:border-white'
              >
                <span>Servicios</span>
                <FontAwesomeIcon
                  icon={faChevronDown}
                  className={`h-5 w-5 ml-1 transition-transform duration-200 group-hover:rotate-180`}
                />
              </Link>
              <div className='absolute hidden group-hover:block top-full pt-2 w-56 bg-transparent'>
                <div
                  className='bg-white/95 backdrop-blur-lg rounded-lg py-2 z-60 border border-white/20 drop-shadow-glow-blue-500  shadow-inner-glow 
                hover:shadow-inner-glow hover:shadow-inner-glow-white-sm
                text-shadow-glow-white'
                >
                  {services.map(service => (
                    <Link
                      key={service.id}
                      to={`/servicios/${service.id}`}
                      className='block px-4 py-2 text-sm  transition-colors rounded-md mx-2 
                      text-blue-700
                      hover:bg-blue-700/80 hover:text-white
                      shadow-inner-glow hover:shadow-inner-glow hover:shadow-inner-glow-white-sm text-shadow-glow-white drop-shadow-glow-white text-shadow-glow-white'
                    >
                      {service.title}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
            <Link
              to='/blog'
              onClick={handleScrollToTopClick}
              className='text-sm font-medium p-2 px-3 rounded-full
              text-blue-700 text-shadow-glow-white drop-shadow-glow-white
              hover:shadow-inner-glow hover:shadow-inner-glow-white-sm
              hover:text-shadow-glow-white hover:text-white hover:bg-blue-700 hover:border-white'
            >
              Blog
            </Link>
            <Link
              to='/contacto'
              onClick={handleScrollToTopClick}
              className='text-sm font-medium p-2 px-3 rounded-full
              text-blue-700 text-shadow-glow-white drop-shadow-glow-white
              hover:shadow-inner-glow hover:shadow-inner-glow-white-sm
              hover:text-shadow-glow-white hover:text-white hover:bg-blue-700 hover:border-white'
            >
              Contacto
            </Link>
            <Link
              to='/empezar'
              onClick={handleScrollToTopClick}
              className='text-blue-700 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200
              border border-[1px] border-blue-700
              hover:text-shadow-glow-white hover:text-white hover:bg-blue-700 hover:border-white
              hover:shadow-inner-glow hover:shadow-inner-glow-white-sm
              text-shadow-glow-white drop-shadow-glow-white '
            >
              Empezar Ahora
            </Link>
          </nav>

          {/* Mobile menu button */}
          <div className='md:hidden'>
            <button
              onClick={toggleMenu}
              className='text-blue-700 hover:text-blue-500 transition-colors p-2'
              aria-label='Toggle menu'
            >
              {isMenuOpen ? (
                <FontAwesomeIcon
                  icon={faTimes}
                  className='h-6 w-6 text-shadow-glow-white text-shadow-glow-white'
                />
              ) : (
                <FontAwesomeIcon
                  icon={faBars}
                  className='h-6 w-6 text-shadow-glow-white text-shadow-glow-white '
                />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMenuOpen && (
          <div className='md:hidden'>
            <nav className='px-4 py-6 space-y-4 flex flex-col'>
              <div className='relative'>
                <div
                  className='flex justify-between items-center w-full cursor-pointer'
                  onClick={toggleMobileServices}
                >
                  <Link
                    to='/servicios'
                    onClick={e => {
                      if (isMobileServicesOpen) {
                        e.preventDefault();
                      } else {
                        closeMenu();
                        handleScrollToTopClick();
                      }
                    }}
                    className='text-blue-700 hover:text-blue-500 transition-colors text-base font-medium py-2 text-shadow-glow-white'
                  >
                    Servicios
                  </Link>
                  <button className='text-blue-700 hover:text-blue-500 p-2'>
                    <FontAwesomeIcon
                      icon={faChevronDown}
                      className={`h-5 w-5 transition-transform ${
                        isMobileServicesOpen ? 'rotate-180' : ''
                      }`}
                    />
                  </button>
                </div>
                {isMobileServicesOpen && (
                  <div className='mt-2'>
                    <div
                      className='bg-white/85 backdrop-blur-lg rounded-lg py-2 border border-white/20 text-shadow-glow-white shadow-inner-glow shadow-inner-glow-white z-'
                      style={{
                        boxShadow:
                          '0 10px 25px -5px rgba(59, 130, 246, 0.2), 0 8px 10px -6px rgba(59, 130, 246, 0.2)',
                      }}
                    >
                      {services.map(service => (
                        <Link
                          key={service.id}
                          to={`/servicios/${service.id}`}
                          onClick={closeMenu}
                          className='block px-4 py-2 text-sm text-blue-700 hover:bg-blue-500/20 transition-colors rounded-md mx-2'
                        >
                          {service.title}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <Link
                to='/blog'
                onClick={() => {
                  closeMenu();
                  handleScrollToTopClick();
                }}
                className='block text-blue-700 hover:text-blue-500 transition-colors text-base font-medium py-2 text-shadow-glow-white'
              >
                Blog
              </Link>
              {/* Enlace a la página de Demos para navegación móvil */}
              {/* <Link
                to='/demos'
                onClick={() => {
                  closeMenu();
                  handleScrollToTopClick();
                }}
                className='block text-blue-700 hover:text-blue-500 transition-colors text-base font-medium py-2 text-shadow-glow-white'
              >
                Demos
              </Link> */}
              <Link
                to='/contacto'
                onClick={() => {
                  closeMenu();
                  handleScrollToTopClick();
                }}
                className='block text-blue-700 hover:text-blue-500 transition-colors text-base font-medium py-2 text-shadow-glow-white'
              >
                Contacto
              </Link>
              <div className='mt-auto pt-4'>
                <Link
                  to='/empezar'
                  onClick={() => {
                    closeMenu();
                    handleScrollToTopClick();
                  }}
                  className='w-full bg-blue-700 hover:bg-blue-500 text-white px-4 py-3 rounded-full text-base font-medium transition-all duration-200 text-shadow-glow-white drop-shadow-glow-white
                  shadow-inner-glow shadow-inner-glow-white text-center block'
                >
                  Empezar Ahora
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
