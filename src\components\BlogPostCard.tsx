import React from 'react';
import { Link } from 'react-router-dom';
import { BlogPost } from '@/constants/blog';
import { getCloudImageUrl } from '@/utils/gcs';

interface BlogPostCardProps {
  post: BlogPost;
}

const BlogPostCard: React.FC<BlogPostCardProps> = ({ post }) => {
  return (
    <Link to={`/blog/${post.slug}`} className='group block'>
      <div className='relative aspect-w-16 aspect-h-9 rounded-2xl overflow-hidden'>
        <img
          className='w-full h-full object-cover rounded-xl transition-all duration-300 group-hover:scale-105 group-hover:rounded-2xl'
          src={getCloudImageUrl(post.imageUrl)}
          alt={post.title}
        />
        <div className='absolute inset-0 bg-blue-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300'></div>
        <div className='absolute inset-0 rounded-2xl shadow-inner-glow shadow-inner-glow-white pointer-events-none'></div>
      </div>
      <div className='mt-6'>
        <p className='text-sm text-gray-500 transition-all duration-300 group-hover:drop-shadow-glow-blue-500'>
          {post.date}
        </p>
        <h2
          className={`text-2xl font-bold mt-2 text-radial-gradient-from-${post.color}-600-to-${post.color}-750 transition-colors duration-300 group-hover:text-blue-500 group-hover:drop-shadow-glow-blue-500`}
        >
          {post.title}
        </h2>
        <p className='mt-4 text-lg text-gray-700 transition-all duration-300 group-hover:drop-shadow-glow-blue-500'>
          {post.description}
        </p>
      </div>
    </Link>
  );
};

export default BlogPostCard;
