import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHeadset,
  faTools,
  faUserPlus,
  faShoppingCart,
  faComments,
  faCalendarAlt,
} from '@fortawesome/free-solid-svg-icons';

const useCases = [
  {
    icon: faHeadset,
    title: 'Atención al cliente 24/7',
    description: 'Soporte ininterrumpido en tu sitio web y redes sociales.',
    bgColor:
      'bg-white shadow-inner-glow shadow-inner-glow-malva-200-sm drop-shadow-glow-malva-200-bottom-md',
  },
  {
    icon: faTools,
    title: 'Soporte técnico de primer nivel',
    description:
      'Resolución automática de preguntas frecuentes y problemas básicos.',
    bgColor:
      'bg-white shadow-inner-glow shadow-inner-glow-malva-200-sm drop-shadow-glow-malva-200-bottom-md',
  },
  {
    icon: faUserPlus,
    title: 'Generación y cualificación de leads',
    description:
      'Captura y cualificación de información de contacto de potenciales clientes.',
    bgColor:
      'bg-white shadow-inner-glow shadow-inner-glow-malva-200-sm drop-shadow-glow-malva-200-bottom-md',
  },
  {
    icon: faShoppingCart,
    title: 'Automatización de ventas',
    description:
      'Guía a los usuarios a través del proceso de compra y cierre de ventas.',
    bgColor:
      'bg-white shadow-inner-glow shadow-inner-glow-malva-200-sm drop-shadow-glow-malva-200-bottom-md',
  },
  {
    icon: faComments,
    title: 'Recopilación de feedback',
    description:
      'Encuestas de satisfacción y recolección de opiniones de clientes.',
    bgColor:
      'bg-white shadow-inner-glow shadow-inner-glow-malva-200-sm drop-shadow-glow-malva-200-bottom-md',
  },
  {
    icon: faCalendarAlt,
    title: 'Gestión de reservas y citas',
    description: 'Automatización completa del proceso de agendamiento.',
    bgColor:
      'bg-white shadow-inner-glow shadow-inner-glow-malva-200-sm drop-shadow-glow-malva-200-bottom-md',
  },
];

const CommonUseCasesSection = () => {
  const highlightStyle = {
    color: 'rgb(255, 255, 255)',
    background:
      'linear-gradient(90deg,rgba(1, 21, 153, 0.8) 0%, rgba(0, 47, 255, 0.8) 10%, rgba(0, 64, 255, 0.8) 90%,  rgba(0, 26, 195, 0.8) 100%)',
    padding: '2px 2px 2px 2px',
    textShadow: '0 0 0px rgba(0, 31, 230, 0.6)',
    borderRadius: '2px',
  };

  return (
    <section className='py-16 bg-gradient-to-b from-white via-malva-50 to-white'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center'>
        <h2 className='text-4xl font-extrabold text-black mb-4'>
          ¿Qué pueden hacer nuestros Agentes de Chat?
        </h2>
        <p className='text-lg text-black mb-8 max-w-3xl mx-auto'>
          Nuestros agentes de chat IA son versátiles y pueden ser implementados
          en diversas áreas para optimizar tus operaciones y mejorar la
          experiencia del cliente. Además, nuestro producto se adapta
          completamente a tus necesidades.
          <span style={highlightStyle}>
            {' '}
            Tú decides cómo usarlo y no te obligamos a usar una interfaz
          </span>
          específica.
        </p>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
          {useCases.map((useCase, index) => (
            <div
              key={index}
              className={`rounded-xl shadow-md p-8 transform transition duration-300 hover:scale-105 hover:shadow-lg hover:ring-4 hover:ring-pink-400 border border-gray-200 ${useCase.bgColor}`}
            >
              <div className='text-5xl text-purple-700 mb-6'>
                <FontAwesomeIcon icon={useCase.icon} />
              </div>
              <h3 className='text-2xl font-bold text-black mb-3'>
                {useCase.title}
              </h3>
              <p className='text-black text-lg'>{useCase.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CommonUseCasesSection;
