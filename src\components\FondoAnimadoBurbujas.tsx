import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

const bubbleData = [
  {
    id: 'bubble1',
    fill: 'rgba(173, 216, 230, 0.5)',
    r: 0.1,
    initialPos: [0.1, 0.1],
  }, // LightBlue
  {
    id: 'bubble2',
    fill: 'rgba(91, 184, 242, 0.5)',
    r: 0.15,
    initialPos: [0.9, 0.2],
  }, // LightSkyBlue
  {
    id: 'bubble3',
    fill: 'rgba(100, 149, 237, 0.5)',
    r: 0.12,
    initialPos: [0.3, 0.8],
  }, // CornflowerBlue
  {
    id: 'bubble4',
    fill: 'rgba(70, 130, 180, 0.5)',
    r: 0.18,
    initialPos: [0.7, 0.9],
  }, // SteelBlue
  {
    id: 'bubble5',
    fill: 'rgba(70, 207, 212, 0.5)',
    r: 0.11,
    initialPos: [0.5, 0.4],
  }, // CadetBlue
  {
    id: 'bubble6',
    fill: 'rgba(65, 105, 225, 0.5)',
    r: 0.14,
    initialPos: [0.2, 0.6],
  }, // RoyalBlue
  {
    id: 'bubble7',
    fill: 'rgba(30, 144, 255, 0.5)',
    r: 0.16,
    initialPos: [0.8, 0.3],
  }, // DodgerBlue
  {
    id: 'bubble8',
    fill: 'rgba(17, 105, 220, 0.5)',
    r: 0.09,
    initialPos: [0.4, 0.05],
  }, // Navy
  {
    id: 'bubble9',
    fill: 'rgba(0, 191, 255, 0.5)',
    r: 0.13,
    initialPos: [0.6, 0.75],
  }, // DeepSkyBlue
  {
    id: 'bubble10',
    fill: 'rgba(33, 33, 165, 0.5)',
    r: 0.1,
    initialPos: [0.05, 0.95],
  }, // MidnightBlue
];

const FondoAnimadoBurbujas = () => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    const svg = svgRef.current;
    if (!svg) return;

    const { clientWidth: width, clientHeight: height } = svg;

    bubbleData.forEach(data => {
      const circleElement = svg.querySelector<SVGCircleElement>(`#${data.id}`);
      if (!circleElement) return;

      // Set initial position and radius
      circleElement.setAttribute('cx', (data.initialPos[0] * width).toString());
      circleElement.setAttribute(
        'cy',
        (data.initialPos[1] * height).toString()
      );
      circleElement.setAttribute(
        'r',
        (data.r * Math.min(width, height)).toString()
      );

      gsap.to(circleElement, {
        x: 'random(-400, 400)', // Increased movement range
        y: 'random(-400, 400)', // Increased movement range
        scale: 'random(1.0, 1.5)',
        opacity: 0.5,
        duration: 'random(10, 20)',
        ease: 'none',
        repeat: -1,
        yoyo: true,
      });
    });

    const blurElement = svg.querySelector('#blur-effect-bubbles');
    if (blurElement) {
      gsap.to(blurElement, {
        attr: { stdDeviation: 'random(10, 20)' },
        duration: 'random(8, 15)',
        ease: 'power1.inOut',
        repeat: -1,
        yoyo: true,
      });
    }
  }, []);

  return (
    <div className='absolute top-0 left-0 w-full h-full overflow-hidden z-0 bg-gradient-to-b from-white from-30% via-blue-750 via-60% to-blue-850 shadow-inner-glow shadow-inner-glow-white-2xl'>
      <svg ref={svgRef} className='w-full h-full'>
        <defs>
          <filter id='background-blur-bubbles'>
            <feGaussianBlur
              in='SourceGraphic'
              stdDeviation='15'
              id='blur-effect-bubbles'
            />
          </filter>
        </defs>

        <g filter='url(#background-blur-bubbles)'>
          {bubbleData.map(data => (
            <circle key={data.id} id={data.id} fill={data.fill} />
          ))}
        </g>
      </svg>
    </div>
  );
};

export default FondoAnimadoBurbujas;
