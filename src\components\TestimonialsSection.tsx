import React from 'react';

export default function TestimonialsSection() {
  const testimonials = [
    {
      quote:
        'La implementación de la IA de Taratic ha transformado nuestra atención al cliente. ¡Increíble!',
      author: '<PERSON>, Gerente de E-commerce',
    },
    {
      quote:
        'Redujimos nuestros costos operativos en un 30% gracias a las soluciones de automatización de Taratic.',
      author: '<PERSON>, Director de Operaciones',
    },
    {
      quote:
        'El equipo de Taratic es excepcional. Su soporte y conocimiento son inigualables.',
      author: '<PERSON>, CEO de Startup Tecnológica',
    },
  ];

  return (
    <section className='py-16 bg-gray-50'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <h2 className='text-4xl font-extrabold text-gray-900 text-center mb-12'>
          Lo Que Dicen Nuestros Clientes
        </h2>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className='bg-white p-8 rounded-lg shadow-lg text-center'
            >
              <p className='text-gray-600 italic mb-4'>
                " {testimonial.quote} "
              </p>
              <p className='text-blue-700 font-semibold'>
                - {testimonial.author}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
