import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faComment, faTimes } from '@fortawesome/free-solid-svg-icons';
import HuggingFaceIcon from '../HuggingFaceIcon';

interface ChatButtonProps {
  onClick: () => void;
}

const ChatButton: React.FC<ChatButtonProps> = ({ onClick }) => {
  const [showBubble, setShowBubble] = useState(false);
  const [bubbleVisible, setBubbleVisible] = useState(true);

  useEffect(() => {
    if (bubbleVisible) {
      const timer = setTimeout(() => {
        setShowBubble(true);
      }, 5000); // Show bubble after 5 seconds

      return () => clearTimeout(timer);
    }
  }, [bubbleVisible]);

  const handleCloseBubble = () => {
    setBubbleVisible(false);
  };

  return (
    <div className='fixed bottom-6 right-6 z-50'>
      {showBubble && bubbleVisible && (
        <div
          onClick={onClick}
          className='absolute bottom-full cursor-pointer shadow-inner-glow shadow-inner-glow-white-md hover:text:blue-700 hover:scale-105 transition-transform duration-300 hover:shadow-inner-glow-blue-700-md hover:bg-white right-0 mb-2 w-max text-white hover:text-blue-800 hover:font-bold p-3 rounded-xl bg-blue-700 flex items-center justify-between'
        >
          ¡Hola! ¿Puedo ayudarte en algo?
          <button
            onClick={e => {
              e.stopPropagation();
              handleCloseBubble();
            }}
            className='ml-2 w-6 h-6 bg-white rounded-full flex items-center justify-center text-blue-700 focus:outline-none shadow-inner-glow shadow-inner-glow-blue-200-sm
            hover:shadow-inner-glow-white-sm hover:text-white hover:bg-blue-700 transform hover:scale-110 transition-all duration-300'
          >
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>
      )}
      <button
        onClick={onClick}
        className='w-16 h-16 flex items-center justify-center text-white hover:shadow-inner-glow-blue-800-md hover:bg-transparent transform hover:scale-110 transition-all duration-300
         drop-shadow-glow-blue-700-sm hover:drop-shadow-glow-blue-700-md hover:rounded-full'
        aria-label='Abrir chat'
      >
        <HuggingFaceIcon
          height='4em'
          width='5em'
          className='drop-shadow-glow-blue-700-'
        />
      </button>
    </div>
  );
};

export default ChatButton;
