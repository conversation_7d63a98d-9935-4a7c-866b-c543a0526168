import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPhoneVolume } from '@fortawesome/free-solid-svg-icons';

import { getCloudImageUrl } from '@/utils/gcs';

interface Message {
  id: number;
  text?: string;
  sender: 'user' | 'commerce';
  imageUrl?: string;
}

interface VoiceAssistantMockupProps {
  startMessagesAnimation: boolean;
  onAnimationComplete?: () => void;
  isMobile?: boolean;
}

const messages: Message[] = [
  {
    id: 1,
    text: '<PERSON><PERSON>, ¿podría ayudarme a reservar una mesa para dos en su restaurante para esta noche?',
    sender: 'user',
  },
  {
    id: 2,
    text: '<PERSON><PERSON><PERSON>, ¿a qué hora le gustaría la reserva?',
    sender: 'commerce',
  },
  {
    id: 3,
    text: 'A las 8 de la noche, por favor.',
    sender: 'user',
  },
  {
    id: 4,
    text: 'Perfecto. ¿A nombre de quién sería la reserva?',
    sender: 'commerce',
  },
  {
    id: 5,
    text: 'A nombre de David.',
    sender: 'user',
  },
  {
    id: 6,
    text: 'Reserva confirmada para David, dos personas, a las 8 PM. ¿Necesita algo más?',
    sender: 'commerce',
  },
  {
    id: 7,
    text: 'No, eso es todo. Muchas gracias.',
    sender: 'user',
  },
  {
    id: 8,
    text: 'De nada. Que tenga una excelente noche.',
    sender: 'commerce',
  },
];

export default function VoiceAssistantMockup({
  startMessagesAnimation,
  onAnimationComplete,
  isMobile = false,
}: VoiceAssistantMockupProps) {
  const messageRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    if (startMessagesAnimation) {
      const duration = isMobile ? 0.3 : 0.5;
      const stagger = isMobile ? 0.15 : 0.25;

      const tl = gsap.timeline({
        delay: 0.5,
        onComplete: () => {
          if (onAnimationComplete) {
            onAnimationComplete();
          }
        },
      });
      messages.forEach((message, index) => {
        tl.fromTo(
          messageRefs.current[index],
          { opacity: 0, y: 20, scale: 0.95 },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: duration,
            ease: 'power2.out',
          },
          `+=${index === 0 ? 0 : stagger}`
        );
      });
    }
  }, [startMessagesAnimation, onAnimationComplete, isMobile]);

  return (
    <div
      className='pointer-events-auto relative w-full max-w-sm mx-auto bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-200 border-4 transform transition-transform duration-500 hover:scale-105 drop-shadow-glow-pink-600-md group cursor-pointer'
      style={{ aspectRatio: '9 / 16' }}
    >
      <div
        className={`absolute inset-0 bg-black bg-opacity-50 backdrop-blur-md flex items-center justify-center opacity-0 ${!isMobile ? 'group-hover:opacity-100' : ''} transition-opacity duration-300 z-10 pointer-events-none ${!isMobile ? 'group-hover:pointer-events-auto' : ''}`}
      >
        <h1 className='text-white text-2xl text-shadow-glow-pink-600 font-bold text-center pointer-events-none'>
          Ver Demo Asistente de Voz
        </h1>
      </div>
      {/* Header */}
      <div className='flex items-center justify-between p-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-md'>
        <div className='flex items-center space-x-3'>
          <div className='w-8 h-8 bg-white rounded-full flex items-center justify-center text-purple-600 font-bold'>
            IA
          </div>
          <span className='font-semibold text-md text-shadow-glow-black-xl'>
            🤖 Asistente de Voz IA
          </span>
        </div>
        <FontAwesomeIcon icon={faPhoneVolume} className='w-5 h-5' />
      </div>

      {/* Chat Body */}
      <div
        className='flex-1 p-3 overflow-y-auto thin-purple-scrollbar'
        style={{ height: 'calc(100% - 56px)' }}
        data-nosnippet='true'
      >
        {messages.map((message, index) => (
          <div
            key={message.id}
            ref={el => (messageRefs.current[index] = el)}
            className={`mb-2 flex ${
              message.sender === 'user' ? 'justify-end' : 'justify-start'
            }`}
            style={{ opacity: 0 }}
          >
            {message.text ? (
              <div
                className={`max-w-[85%] px-3 py-1.5 rounded-xl text-sm ${
                  message.sender === 'user'
                    ? 'bg-blue-500 text-white rounded-br-none'
                    : 'bg-gray-200 text-gray-800 rounded-bl-none'
                }`}
              >
                {message.text}
              </div>
            ) : message.imageUrl ? (
              <div className='max-w-[60%]'>
                <img
                  src={getCloudImageUrl(message.imageUrl)}
                  alt='Integraciones de voz'
                  className='rounded-xl'
                />
              </div>
            ) : null}
          </div>
        ))}
      </div>
    </div>
  );
}
