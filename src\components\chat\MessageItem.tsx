import React, { useEffect, useState } from 'react';

interface MessageItemProps {
  message: {
    id: number;
    text?: string;
    sender: 'bot' | 'user';
    audioBlob?: Blob;
    file?: File;
  };
}

const MessageItem: React.FC<MessageItemProps> = ({ message }) => {
  const [audioUrl, setAudioUrl] = useState<string | undefined>(undefined);
  const [fileUrl, setFileUrl] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (message.audioBlob) {
      const url = URL.createObjectURL(message.audioBlob);
      setAudioUrl(url);
      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [message.audioBlob]);

  useEffect(() => {
    if (message.file) {
      const url = URL.createObjectURL(message.file);
      setFileUrl(url);
      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [message.file]);

  const isImage = (file: File) => {
    return file.type.startsWith('image/');
  };

  const getFileIcon = (file: File) => {
    if (file.type.includes('pdf')) return '📄';
    if (file.type.includes('word') || file.type.includes('document'))
      return '📝';
    if (file.type.includes('text')) return '📄';
    return '📎';
  };

  return (
    <div
      className={`flex ${message.sender === 'bot' ? 'justify-start' : 'justify-end'} my-2`}
    >
      <div
        key={message.id}
        className={`inline-block p-3 rounded-lg max-w-xs sm:max-w-sm md:max-w-md break-words overflow-hidden ${
          message.sender === 'bot'
            ? 'bg-gray-100 text-gray-800 shadow-inner-glow shadow-inner-glow-white-md'
            : 'bg-blue-300 text-gray-900 text-shadow-glow-white-sm shadow-inner-glow shadow-inner-glow-white-md'
        }`}
      >
        {message.text && (
          <span className='text-shadow-glow-white-xs block whitespace-pre-wrap word-break break-words'>
            {message.text}
          </span>
        )}
        {audioUrl && (
          <audio controls src={audioUrl} className='w-full mt-2'></audio>
        )}
        {message.file && fileUrl && (
          <div className='mt-2'>
            {isImage(message.file) ? (
              <div>
                <img
                  src={fileUrl}
                  alt={message.file.name}
                  className='max-w-full max-h-48 rounded-lg object-contain'
                  loading='lazy'
                  onLoad={() => {
                    // Trigger scroll when image loads
                    setTimeout(() => {
                      const event = new CustomEvent('imageLoaded');
                      window.dispatchEvent(event);
                    }, 50);
                  }}
                />
                <p className='text-xs text-gray-600 mt-1 truncate'>
                  {message.file.name}
                </p>
              </div>
            ) : (
              <div className='flex items-center p-2 bg-gray-100 rounded-lg'>
                <span className='text-2xl mr-2 flex-shrink-0'>
                  {getFileIcon(message.file)}
                </span>
                <div className='flex-1 min-w-0'>
                  <p className='text-sm font-medium text-gray-800 truncate'>
                    {message.file.name}
                  </p>
                  <p className='text-xs text-gray-500'>
                    {(message.file.size / 1024).toFixed(1)} KB
                  </p>
                </div>
                <a
                  href={fileUrl}
                  download={message.file.name}
                  className='ml-2 text-blue-500 hover:text-blue-700 text-sm flex-shrink-0'
                >
                  Descargar
                </a>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageItem;
