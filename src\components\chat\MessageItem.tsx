import React, { useEffect, useState } from 'react';

interface MessageItemProps {
  message: {
    id: number;
    text?: string;
    sender: 'bot' | 'user';
    audioBlob?: Blob;
  };
}

const MessageItem: React.FC<MessageItemProps> = ({ message }) => {
  const [audioUrl, setAudioUrl] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (message.audioBlob) {
      const url = URL.createObjectURL(message.audioBlob);
      setAudioUrl(url);
      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [message.audioBlob]);

  return (
    <div
      key={message.id}
      className={`my-2 p-3 rounded-lg max-w-xs break-words' ${
        message.sender === 'bot'
          ? 'bg-gray-100 text-gray-800 shadow-inner-glow shadow-inner-glow-white-md'
          : 'bg-blue-300 text-gray-900 ml-auto text-shadow-glow-white-sm shadow-inner-glow shadow-inner-glow-white-md'
      }`}
    >
      {message.text && (
        <span className='text-shadow-glow-white-xs'>{message.text}</span>
      )}
      {audioUrl && (
        <audio controls src={audioUrl} className='w-full mt-2'></audio>
      )}
    </div>
  );
};

export default MessageItem;
