import React from 'react';

const WhatsAppConfirmationSection = () => {
  return (
    <section className='my-12 py-12 bg-white rounded-lg shadow-inner-glow min-h-screen flex items-center justify-center'>
      <div className='max-w-4xl mx-auto text-center px-4'>
        <h2 className='text-3xl font-bold text-green-800 mb-6'>
          Confirmación Instantánea por WhatsApp
        </h2>
        <p className='text-lg text-gray-700 mb-8'>
          Una vez confirmada la reserva, tu cliente recibe un mensaje automático
          por WhatsApp, asegurando su asistencia y reduciendo los no-shows.
        </p>
        <div className='max-w-sm mx-auto bg-gray-800 rounded-3xl border-4 border-gray-700 p-2'>
          <div className='bg-white rounded-2xl overflow-hidden'>
            <div className='bg-gray-100 p-3 flex items-center gap-3 border-b'>
              <div className='w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold'>
                T
              </div>
              <div>
                <p className='font-bold text-black'>Tu Restaurante</p>
                <p className='text-sm text-gray-600'>online</p>
              </div>
            </div>
            <div className='p-4 h-96 flex items-center'>
              <div className='flex justify-start w-full'>
                <div className='bg-white rounded-lg rounded-tl-none shadow-md max-w-xs overflow-hidden'>
                  <img
                    src='https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&w=2070&auto=format&fit=crop'
                    alt='Interior de un restaurante elegante'
                    className='w-full h-32 object-cover'
                  />
                  <div className='p-3'>
                    <p className='font-bold text-gray-900'>
                      ¡Hola! Tu reserva en <strong>Tu Restaurante</strong> está
                      casi lista.
                    </p>
                    <p className='mt-2 text-gray-800'>
                      <strong>Detalles:</strong> 2 personas, sábado a las 21:00.
                    </p>
                    <p className='text-sm text-gray-600 mt-2'>
                      Para asegurar tu mesa, por favor, confirma tu reserva a
                      continuación.
                    </p>
                  </div>
                  <div className='bg-gray-50 border-t border-gray-200 grid grid-cols-2 divide-x divide-gray-200'>
                    <button className='py-3 text-blue-500 font-semibold hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500'>
                      Confirmar
                    </button>
                    <button className='py-3 text-blue-500 font-semibold hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500'>
                      Cancelar
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhatsAppConfirmationSection;
