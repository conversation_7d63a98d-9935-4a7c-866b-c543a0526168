import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMicrophone } from '@fortawesome/free-solid-svg-icons';

const HeroSectionAgentesDeVoz = () => {
  return (
    <header className='relative bg-gradient-to-b mt-2 pb-6 from-white via-green-600 via-70% to-green-800 iphone-xr-pro-max:py-4 shadow-inner-glow shadow-inner-glow-white-left-2xl shadow-inner-glow-white-right-2xl'>
      <div className='max-w-7xl mx-auto px-6 sm:px-6 lg:px-8 text-center mt-6'>
        <FontAwesomeIcon
          icon={faMicrophone}
          className='h-16 w-16 text-white mx-auto mb-4 text-shadow-glow-green-900-sm drop-shadow-glow-green-800-md'
        />
        <h1
          className='text-6xl font-bold tracking-xl font-extrabold tracking-tight
        text-white drop-shadow-glow-green-800-md text-shadow-glow-green-900-sm'
        >
          <PERSON>es de <PERSON>oz IA
        </h1>
        <p className='mt-2 text-lg md:text-xl mx-auto text-white drop-shadow-glow-green-800-md max-w-6xl text-shadow-glow-green-900-sm'>
          Transforma tu atención telefónica con la inteligencia artificial.
          Nuestros Agentes de Voz IA gestionan consultas, reservas y soporte
          técnico con la fluidez de una conversación humana, liberando a tu
          equipo y maximizando tu eficiencia.
        </p>
      </div>
    </header>
  );
};

export default HeroSectionAgentesDeVoz;
