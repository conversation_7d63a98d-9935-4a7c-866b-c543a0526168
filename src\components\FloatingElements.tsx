import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronUp } from '@fortawesome/free-solid-svg-icons';
import { faWhatsapp } from '@fortawesome/free-brands-svg-icons';

export default function FloatingElements() {
  const [showBackToTop, setShowBackToTop] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const openWhatsApp = () => {
    window.open(
      'https://wa.me/34828624064?text=Hola, me interesa conocer más sobre sus servicios de IA',
      '_blank'
    );
  };

  return (
    <>
      {/* WhatsApp Button - Comentado */}
      {/*
      <button
        onClick={openWhatsApp}
        className='fixed bottom-28 right-6 bg-green-700 text-white w-16 h-16 rounded-full
        shadow-inner-glow shadow-inner-glow-white-sm hover:shadow-inner-glow-green-700-md hover:bg-white hover:text-green-800 hover:text-shadow-glow-green-800 transform hover:scale-110 transition-all duration-300 z-50 drop-shadow-glow-green'
        aria-label='Contactar por WhatsApp'
      >
        <FontAwesomeIcon
          icon={faWhatsapp}
          className='h-8 w-8 text-center align-middle p-1'
        />
      </button>
      */}

      {/* Back to Top Button */}
      {showBackToTop && (
        <button
          onClick={scrollToTop}
          className='fixed bottom-6 left-6 
          bg-blue-600 
          text-blue-700 drop-shadow-glow-blue-700 bg-white
          border border-[1.5px] border-blue-700
          hover:bg-blue-700 hover:text-white hover:scale-125 hover:border-1 hover:border-white hover:shadow-inner-glow hover:shadow-inner-glow-white-sm
          p-3 rounded-full
          transform transition-all duration-300 z-50'
          aria-label='Volver arriba'
        >
          <FontAwesomeIcon
            icon={faChevronUp}
            className='h-5 w-5 text-center align-middle p-1'
          />
        </button>
      )}

      {/* Cookie Consent */}
      {/* {showCookieConsent && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50 p-4">
          <div className="max-w-7xl mx-auto flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
            <p className="text-sm text-gray-600 text-center sm:text-left">
              Utilizamos cookies para mejorar tu experiencia. Al continuar navegando, aceptas nuestro uso de cookies.
            </p>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowCookieConsent(false)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Aceptar
              </button>
              <button
                onClick={() => setShowCookieConsent(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
                aria-label="Cerrar aviso de cookies"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      )} */}
    </>
  );
}
