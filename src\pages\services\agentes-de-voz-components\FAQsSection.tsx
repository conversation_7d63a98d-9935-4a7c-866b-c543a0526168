import React from 'react';
import FAQSection from '../../../components/FAQSection';

const agentesDeVozFaqs = [
  {
    question:
      '¿Qué tipo de negocios pueden beneficiarse de un Agente de Voz IA?',
    answer:
      '<PERSON>ualquier negocio que maneje un alto volumen de llamadas telefónicas, como restaurantes, clínicas, servicios a domicilio, centros de atención al cliente, etc., puede beneficiarse enormemente de la automatización y eficiencia que ofrece un Agente de Voz IA.',
    highlightPhrase: '',
  },
  {
    question:
      '¿Es difícil integrar el Agente de Voz IA con mis sistemas actuales?',
    answer:
      'Nuestros Agentes de Voz IA están diseñados para una integración flexible. Podemos conectarlos con tus sistemas CRM, bases de datos de reservas, y otras herramientas de gestión existentes para asegurar un flujo de trabajo sin interrupciones.',
    highlightPhrase: '',
  },
  {
    question:
      '¿Cómo maneja el asistente las conversaciones complejas o inesperadas?',
    answer:
      'Gracias a la tecnología de VAPI y un backend inteligente, el asistente está entrenado para entender una amplia gama de intenciones y contextos. En casos de conversaciones muy complejas o fuera de su alcance, puede escalar la llamada a un agente humano de forma fluida.',
    highlightPhrase: '',
  },
  {
    question: '¿Qué tan seguro es el manejo de datos de mis clientes?',
    answer:
      'La seguridad y privacidad de los datos son nuestra máxima prioridad. Implementamos protocolos de seguridad avanzados y cumplimos con las normativas de protección de datos para asegurar que toda la información de tus clientes esté protegida.',
    highlightPhrase: '',
  },
];

interface FAQsSectionProps {
  shadowColorClass?: string;
}

const FAQsSection: React.FC<FAQsSectionProps> = ({ shadowColorClass }) => {
  return (
    <section className='my-12 py-6 bg-white rounded-lg shadow-inner-glow'>
      <FAQSection faqs={agentesDeVozFaqs} shadowColorClass={shadowColorClass} />
    </section>
  );
};
export default FAQsSection;
